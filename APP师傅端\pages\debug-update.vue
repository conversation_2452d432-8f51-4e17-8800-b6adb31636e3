<template>
  <view class="debug-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">‹</text>
      </view>
      <view class="nav-title">更新功能调试</view>
      <view class="nav-right"></view>
    </view>

    <!-- 基本信息 -->
    <view class="info-section">
      <view class="info-title">基本信息</view>
      <view class="info-item">
        <text class="info-label">当前版本:</text>
        <text class="info-value">{{ currentVersion }}</text>
      </view>
      <view class="info-item">
        <text class="info-label">平台类型:</text>
        <text class="info-value">{{ platform }} (师傅端)</text>
      </view>
      <view class="info-item">
        <text class="info-label">系统信息:</text>
        <text class="info-value">{{ systemInfo }}</text>
      </view>
    </view>

    <!-- 测试按钮 -->
    <view class="test-section">
      <view class="test-title">功能测试</view>
      
      <button class="test-btn" @click="testGetVersion" :disabled="isLoading">
        测试获取版本号
      </button>
      
      <button class="test-btn" @click="testApiCall" :disabled="isLoading">
        测试API调用
      </button>
      
      <button class="test-btn" @click="testAppUpdateMethod" :disabled="isLoading">
        测试appUpdate.checkUpdate
      </button>
      
      <button class="test-btn" @click="testAppLaunchMethod" :disabled="isLoading">
        测试App启动检查方法
      </button>
      
      <button class="test-btn" @click="clearLogs">
        清空日志
      </button>
    </view>

    <!-- 日志显示 -->
    <view class="log-section">
      <view class="log-title">调试日志</view>
      <view class="log-content">
        <text 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
          :class="log.type"
        >
          {{ log.message }}
        </text>
      </view>
    </view>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'
import $api from '@/api/index.js'

export default {
  name: 'DebugUpdate',
  data() {
    return {
      currentVersion: '未知',
      platform: 1,
      systemInfo: '获取中...',
      isLoading: false,
      logs: []
    }
  },
  onLoad() {
    this.initDebugInfo()
  },
  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 初始化调试信息
     */
    async initDebugInfo() {
      this.addLog('=== 开始初始化调试信息 ===', 'info')
      
      try {
        // 获取系统信息
        const sysInfo = uni.getSystemInfoSync()
        this.systemInfo = `${sysInfo.platform} ${sysInfo.appVersion || '未知'}`
        this.addLog(`系统信息: ${JSON.stringify(sysInfo)}`, 'info')
        
        // 获取当前版本
        this.currentVersion = await appUpdate.getCurrentVersion()
        this.addLog(`当前版本: ${this.currentVersion}`, 'info')
        
        this.addLog('=== 初始化完成 ===', 'success')
      } catch (error) {
        this.addLog(`初始化失败: ${error.message}`, 'error')
      }
    },

    /**
     * 添加日志
     */
    addLog(message, type = 'info') {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift({
        message: `[${time}] ${message}`,
        type: type
      })
      if (this.logs.length > 100) {
        this.logs = this.logs.slice(0, 100)
      }
      console.log(`[DEBUG] ${message}`)
    },

    /**
     * 清空日志
     */
    clearLogs() {
      this.logs = []
    },

    /**
     * 测试获取版本号
     */
    async testGetVersion() {
      this.isLoading = true
      this.addLog('=== 测试获取版本号 ===', 'info')
      
      try {
        const version = await appUpdate.getCurrentVersion()
        this.addLog(`获取版本成功: ${version}`, 'success')
        this.currentVersion = version
      } catch (error) {
        this.addLog(`获取版本失败: ${error.message}`, 'error')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 测试API调用
     */
    async testApiCall() {
      this.isLoading = true
      this.addLog('=== 测试API调用 ===', 'info')
      
      try {
        const response = await $api.user.checkAppVersion({
          version: this.currentVersion,
          platform: this.platform
        })
        
        this.addLog(`API调用成功: ${JSON.stringify(response)}`, 'success')
        
        if (response.code === '200' && response.data) {
          const updateInfo = response.data
          if (updateInfo.needUpdate) {
            this.addLog(`发现新版本: v${updateInfo.latestVersion}`, 'info')
            this.addLog(`更新描述: ${updateInfo.description}`, 'info')
            this.addLog(`是否强制更新: ${updateInfo.forceUpdate}`, 'info')
          } else {
            this.addLog('已是最新版本', 'info')
          }
        } else {
          this.addLog(`API返回错误: ${response.msg}`, 'warning')
        }
      } catch (error) {
        this.addLog(`API调用失败: ${error.message}`, 'error')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 测试appUpdate.checkUpdate方法
     */
    async testAppUpdateMethod() {
      this.isLoading = true
      this.addLog('=== 测试appUpdate.checkUpdate方法 ===', 'info')
      
      try {
        const result = await appUpdate.checkUpdate({
          silent: true,
          showLoading: false
        })
        
        if (result) {
          this.addLog(`检查更新成功，有新版本: ${JSON.stringify(result)}`, 'success')
        } else {
          this.addLog('检查更新成功，已是最新版本', 'success')
        }
      } catch (error) {
        this.addLog(`检查更新失败: ${error.message}`, 'error')
      } finally {
        this.isLoading = false
      }
    },

    /**
     * 测试App启动检查方法
     */
    async testAppLaunchMethod() {
      this.isLoading = true
      this.addLog('=== 测试App启动检查方法 ===', 'info')
      
      try {
        // 模拟App.vue中的checkAppUpdateOnLaunch方法
        this.addLog('=== 开始检查APP更新 ===', 'info')

        // 获取当前版本
        const currentVersion = await appUpdate.getCurrentVersion()
        this.addLog(`当前版本: ${currentVersion}`, 'info')

        // 调用后端接口检查更新
        const response = await $api.user.checkAppVersion({
          version: currentVersion,
          platform: 1 // 师傅端
        })

        this.addLog(`版本检查响应: ${JSON.stringify(response)}`, 'info')

        if (response.code === '200' && response.data) {
          const updateInfo = response.data
          this.addLog(`更新信息: ${JSON.stringify(updateInfo)}`, 'info')

          if (updateInfo.needUpdate) {
            this.addLog('=== 发现新版本，显示更新提醒 ===', 'success')
            // 这里不实际显示弹窗，只记录日志
            this.addLog('模拟显示更新对话框', 'info')
          } else {
            this.addLog('=== 已是最新版本，无需更新 ===', 'success')
          }
        } else {
          this.addLog(`检查更新失败: ${response.msg || '未知错误'}`, 'warning')
          this.addLog('尝试使用默认更新检查方法...', 'info')
          await appUpdate.checkUpdate({ silent: true, showLoading: false })
        }
      } catch (error) {
        this.addLog(`检查更新异常: ${error.message}`, 'error')
        try {
          this.addLog('异常后尝试使用默认更新检查方法...', 'info')
          await appUpdate.checkUpdate({ silent: true, showLoading: false })
        } catch (fallbackError) {
          this.addLog(`默认更新检查也失败: ${fallbackError.message}`, 'error')
        }
      } finally {
        this.isLoading = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.debug-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  
  .nav-left, .nav-right {
    width: 80rpx;
  }
  
  .nav-icon {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
  }
  
  .nav-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }
}

.info-section, .test-section {
  background: #fff;
  margin: 20rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}

.info-title, .test-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.info-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  
  .info-label {
    font-size: 28rpx;
    color: #666;
  }
  
  .info-value {
    font-size: 28rpx;
    color: #333;
    flex: 1;
    text-align: right;
    word-break: break-all;
  }
}

.test-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  margin-bottom: 16rpx;
  
  &:disabled {
    background: #ccc;
  }
  
  &:active:not(:disabled) {
    opacity: 0.8;
  }
}

.log-section {
  background: #fff;
  margin: 0 20rpx 20rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}

.log-title {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}

.log-content {
  max-height: 800rpx;
  overflow-y: auto;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
}

.log-item {
  display: block;
  font-size: 24rpx;
  line-height: 1.6;
  margin-bottom: 8rpx;
  word-break: break-all;
  
  &.info {
    color: #666;
  }
  
  &.success {
    color: #52c41a;
  }
  
  &.warning {
    color: #faad14;
  }
  
  &.error {
    color: #f5222d;
  }
}
</style>
