<view class="debug-page data-v-64d774fc"><view class="navbar data-v-64d774fc"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-64d774fc" bindtap="__e"><text class="nav-icon data-v-64d774fc">‹</text></view><view class="nav-title data-v-64d774fc">更新功能调试</view><view class="nav-right data-v-64d774fc"></view></view><view class="info-section data-v-64d774fc"><view class="info-title data-v-64d774fc">基本信息</view><view class="info-item data-v-64d774fc"><text class="info-label data-v-64d774fc">当前版本:</text><text class="info-value data-v-64d774fc">{{currentVersion}}</text></view><view class="info-item data-v-64d774fc"><text class="info-label data-v-64d774fc">平台类型:</text><text class="info-value data-v-64d774fc">{{platform+" (师傅端)"}}</text></view><view class="info-item data-v-64d774fc"><text class="info-label data-v-64d774fc">系统信息:</text><text class="info-value data-v-64d774fc">{{systemInfo}}</text></view></view><view class="test-section data-v-64d774fc"><view class="test-title data-v-64d774fc">功能测试</view><button class="test-btn data-v-64d774fc" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testGetVersion',['$event']]]]]}}" bindtap="__e">测试获取版本号</button><button class="test-btn data-v-64d774fc" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testApiCall',['$event']]]]]}}" bindtap="__e">测试API调用</button><button class="test-btn data-v-64d774fc" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testAppUpdateMethod',['$event']]]]]}}" bindtap="__e">测试appUpdate.checkUpdate</button><button class="test-btn data-v-64d774fc" disabled="{{isLoading}}" data-event-opts="{{[['tap',[['testAppLaunchMethod',['$event']]]]]}}" bindtap="__e">测试App启动检查方法</button><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="test-btn data-v-64d774fc" bindtap="__e">清空日志</button></view><view class="log-section data-v-64d774fc"><view class="log-title data-v-64d774fc">调试日志</view><view class="log-content data-v-64d774fc"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><text class="{{['log-item','data-v-64d774fc',log.type]}}">{{''+log.message+''}}</text></block></view></view></view>