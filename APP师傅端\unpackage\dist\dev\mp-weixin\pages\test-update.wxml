<view class="test-update-page data-v-59b4b70a"><view class="navbar data-v-59b4b70a"><view data-event-opts="{{[['tap',[['goBack',['$event']]]]]}}" class="nav-left data-v-59b4b70a" bindtap="__e"><text class="nav-icon data-v-59b4b70a">‹</text></view><view class="nav-title data-v-59b4b70a">更新测试</view><view class="nav-right data-v-59b4b70a"></view></view><view class="version-info data-v-59b4b70a"><text class="version-text data-v-59b4b70a">{{"当前版本: v"+currentVersion}}</text></view><view class="test-buttons data-v-59b4b70a"><button class="test-btn data-v-59b4b70a" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['testSilentCheck',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'测试静默检查')+''}}</button><button class="test-btn data-v-59b4b70a" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['testNormalCheck',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'测试普通检查')+''}}</button><button class="test-btn data-v-59b4b70a" disabled="{{isChecking}}" data-event-opts="{{[['tap',[['testDirectAPI',['$event']]]]]}}" bindtap="__e">{{''+(isChecking?'检查中...':'测试直接API调用')+''}}</button></view><view class="log-section data-v-59b4b70a"><view class="log-title data-v-59b4b70a">测试日志</view><view class="log-content data-v-59b4b70a"><block wx:for="{{logs}}" wx:for-item="log" wx:for-index="index" wx:key="index"><text class="log-item data-v-59b4b70a">{{''+log+''}}</text></block></view><button data-event-opts="{{[['tap',[['clearLogs',['$event']]]]]}}" class="clear-btn data-v-59b4b70a" bindtap="__e">清空日志</button></view></view>