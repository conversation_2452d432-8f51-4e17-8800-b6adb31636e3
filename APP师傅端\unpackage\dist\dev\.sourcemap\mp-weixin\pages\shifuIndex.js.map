{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?2568", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?a336", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?7037", "uni-app:///pages/shifuIndex.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?376e", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/shifuIndex.vue?96cd"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "components", "tabbar", "data", "orderData", "showDing<PERSON><PERSON>", "showCate", "infodata", "tmplIds", "status", "id", "shifuId", "currentTab", "tabsList", "name", "badge", "listType", "list", "confirmshow", "masterModalShow", "detailModalShow", "content", "input", "area_id", "limit", "page", "bannerList", "configInfo", "getconfigs", "list1", "lng", "shi<PERSON><PERSON><PERSON>us", "msg", "QuotationCounts", "lat", "cateList", "currentCateId", "currentCateName", "copyCateList", "province", "city", "district", "isPageLoaded", "selectedItem", "priceRange", "type", "min", "max", "customMin", "customMax", "distance", "appliedDistance", "isPriceCollapsed", "isDistanceCollapsed", "isCategoryCollapsed", "showFilter", "currentActivityType", "areaCount", "computed", "configInfos", "regeocode", "refreshReceiving", "priceFilterText", "isPriceFilterActive", "isDistanceFilterActive", "isCategoryFilterActive", "methods", "textclick", "switchTab", "getOrderType", "getMenuType", "getListByTab", "uni", "title", "apiParams", "pageNum", "pageSize", "parentId", "menu", "minPrice", "maxPrice", "userId", "quotationNum", "Object", "res", "console", "icon", "count", "reset", "closeCate", "setTimeout", "chooseCate", "selectClick", "getCate", "seeDetail", "confirmDetail", "url", "getList", "location", "longitude", "latitude", "success", "fail", "geoRes", "locationData", "county", "address", "defaultLocationData", "handleReceive", "confirmRe", "order_id", "duration", "goToSettle", "getServiceInfo", "city_id", "onReachBottom", "initializePage", "selectPriceRange", "validatePriceInput", "matchedPredefined", "validateDistanceInput", "applyFilters", "buildFilterParams", "toggleCollapse", "toggleFilter", "closeFilter", "selectActivityType", "applyActivityFilter", "resetActivityFilter", "selectArea", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "reset<PERSON>rea<PERSON><PERSON>er", "selectSort", "applySortFilter", "resetSortFilter", "applyAdvancedFilter", "resetAdvancedFilter", "fetchFilteredData", "applyPriceFilter", "resetPriceFilter", "applyDistanceFilter", "resetDistanceFilter", "applyCategoryFilter", "resetCate<PERSON><PERSON><PERSON><PERSON><PERSON>", "getQuotationCounts", "onLoad", "onPullDownRefresh", "onShow", "onHide", "onUnload"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,mBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,iSAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,qRAEN;AACP,KAAK;AACL;AACA,aAAa,2RAEN;AACP,KAAK;AACL;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACpFA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;;ACwO72B;AAGA;AAAA;AAAA;EAAA;IAAA;EAAA;AAAA;AAAA,eACA;EACAC;IACAC;EACA;EACAC;IACA;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC,UACA,gDACA,+CACA,8CACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC,WACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;QAAAF;MAAA;QAAAA;MAAA;QAAAA;MAAA;MACAG;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;QACA5B;QACA6B;MACA;MACAC;MACAC;MACAC;MACAC;MACAC;MAAA;MACAC;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;QACAC;QAAA;QACAC;QACAC;QACAC;QACAC;MACA;MACAC;MAAA;MACAC;MAAA;MACA;MACAC;MACAC;MACAC;MACA;MACAC;MACAC;MAEAC;IACA;EACA;EACAC,0CACA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;IACAC;MAAA;IAAA;EACA;IACAC;MACA;QACA;MACA;QACA;MACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;UACA;QACA;QACA;MACA;;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;IACAC;MACA;IACA;EAAA,EACA;;EACAC;IACAC;MACA;IACA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;QACA;UAAA;MAAA;IAEA;IAEA;IACAC;MACA;QACA;UAAA;QAAA;QACA;UAAA;QAAA;MAAA;IAEA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAC;kBACAC;gBACA;gBAAA;gBAEAC;kBACA5C;kBACAI;kBACAyC;kBACAC;kBACAC;kBACAhC;kBAAA;kBACAiC;kBAAA;kBACA5B;kBAAA;kBACA6B;kBACAC;kBACAC;kBAAA;kBACAC;gBACA,GAEA;;gBACAC;kBAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAC;gBACAC;gBACA;kBACAb;oBACAc;oBACAb;kBACA;gBACA;gBAEA;gBACAc;gBACAf;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAa;gBACA;gBACA;cAAA;gBAAA;gBAEAb;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEAgB;MACA;MACA;MACA;QACA3C;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAwC;MAAA;MACA;MACAC;QACA;MACA;IACA;IACA;IACAC;MACA;IACA;IAEAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAAT;gBACAC;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAb;kBACAc;kBACAb;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAqB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBAEAb;gBACA;cAAA;gBAHAG;gBAIA;gBACAC;gBACA;gBAAA,MACA;kBAAA;kBAAA;gBAAA;gBACAb;kBACAc;kBACAb;gBACA;gBAAA;cAAA;gBAGA;cAAA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAGAY;gBACAb;kBACAc;kBACAb;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAsB;MACA;QACAV;QACAb;QACAA;UACAwB;QACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAzB;kBACAC;gBACA;gBAAA;gBAEAyB;kBACAC;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;kBACA5B;oBACA3B;oBACAwD;oBACAC;kBACA;gBACA;cAAA;gBANAJ;gBAOA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA1B;kBACAc;kBACAb;gBACA;cAAA;gBAAA;gBAAA;gBAAA,OAGA;kBACAD;oBACAwB;oBACAK;oBACAC;kBACA;gBACA;cAAA;gBANAC;gBAOA;kBACA3C;kBACA1B;kBACAJ;gBACA;gBACAuD;gBACA;gBACA;gBACA;gBACA;;gBAEA;gBACAmB;kBACAjE;kBACAC;kBACAiE;kBAAA;kBACAC;kBACA5E;kBACAI;gBACA;gBACAsC;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;;gBAEA;gBACAmC;kBACApE;kBACAC;kBACAiE;kBACAC;kBACA5E;kBACAI;gBACA;gBACAmD;gBACAb;cAAA;gBAAA;gBAAA,OAIA;kBACA1C;kBACAI;kBACA2C;kBACAF;kBACAC;kBACA1B;kBAAA;kBACA4B;kBAAA;kBACAG;kBAAA;kBACAC;kBACA;gBACA;cAAA;gBAXAE;gBAYAC;gBACA;kBACAb;oBACAc;oBACAb;kBACA;gBACA;gBACAY;gBACA;gBACAE;gBACAf;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAa;gBACA;gBACA;cAAA;gBAAA;gBAEAb;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACAoC;MACA;MACA;MACA;MACA;QAAA;QACA;MACA;IACA;IACAC;MAAA;MACA;MACA;QACAC;MACA;QACA;QACAtC;UACAc;UACAb;UACAsC;QACA;QACArB;UACAlB;YACAwB;UACA;QACA;MACA;QACAxB;UACAc;UACAb;QACA;MACA;IACA;IACAuC;MACA;MACAxC;QACAwB;MACA;IACA;IACAiB;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;kBACAC;gBACA;cAAA;gBAFA9B;gBAGAC;gBACA;gBACA;kBAAA;gBAAA;gBACA;kBACA,gBACA,oFACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEA;gBACA;gBACA;gBACA;gBACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA8B;MAAA;MACA;MACA;MACA;MACA;QACAxC;QACAC;QACAC;QACA3C;QACAJ;QACAoB;QAAA;QACA6B;QACAC;QACAC;QAAA;QACAC;MACA;;MAEA;MACAR;MACAA;;MAEA;MACAS;QAAA;MAAA;MAEA;QACA;UACAE;UACA;UACAb;YACAc;YACAb;UACA;UACA;QACA;QACA;QACA;UACA;QACA;UACA;QACA;MACA;QACA;QACAD;UACAc;UACAb;QACA;MACA;IACA;IACA2C;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBACA5C;kBACAC;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;gBAAA,OACA;cAAA;gBAAA;;gBAEA;gBACA;kBACA;gBACA;gBAEA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAY;gBACAb;kBACAc;kBACAb;gBACA;cAAA;gBAAA;gBAEAD;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACA6C;MACA;QACAxE;QACAC;QACAC;QACAC;QACAC;MACA;IACA;IAEAqE;MACA;QACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;QACA;QACA;UACA;QACA;UACA;UACA;QACA;MACA;;MAEA;MACA;MACA;MAEA;QACA;;QAEA;QACA,wBACA;UAAAxE;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,GACA;UAAAD;UAAAC;QAAA,EACA;QACA;QACA;UAAA;UACA;YACA;YACAwE;YACA;UACA;QACA;QACA;UACA;QACA;MACA;QACA;MACA;QACA;QACA;MACA;IACA;IAEAC;MACA;MACA;QACA;QACAhD;UACAc;UACAb;QACA;MACA;QACA;QACAD;UACAc;UACAb;QACA;MACA;QACA;MACA;IACA;IAEA;IACAgD;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;QACA5F;QACAI;QACAyC;QAAA;QACAC;QACAC;QACA3B;QACA+B;QAAA;QACAC;MACA;;MAEA;MACA;QACAR;QACAA;MACA;;MAEA;MACAA;MACAA;;MAEA;MACAS;QAAA;MAAA;MAEA;IACA;IAEA;IACAwC;MACA;QACA;MACA;QACA;MACA;QACA;MACA;IACA;IACA;IACAC;MACA;QACA;MACA;QACA;MACA;IACA;IAEAC;MACA;IACA;IACA;IACA;IACAC;MACA;IACA;IACAC;MACA;MACA;QACArD;MACA;MACA;MACA;IACA;IACAsD;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACAxD;QACA;MACA;MACA;MACA;IACA;IACAyD;MACA;IACA;IACAC;MACA;IACA;IACAC;MAAA;MACA;MACA;QACA;UAAA;QAAA;QACA;UACA3D;QACA;MACA;MACA;MACA;IACA;IACA4D;MACA;IACA;IAEAC;MACA;MACA;MACA;MACA;IACA;IAEAC;MACA;QACA3F;QACAC;QACAC;QACAC;QACAC;MACA;MACA;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IAEA;IACAwF;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACAjE;kBACAC;gBACA;gBACA;gBACA;;gBAEA;gBACA;gBACA;gBACA;gBAAA;gBAAA;gBAAA,OAGA;cAAA;gBAAAW;gBACAC;gBACA;kBACAb;oBACAc;oBACAb;kBACA;gBACA;gBAEA;gBACAc;gBACAf;gBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAa;gBACA;gBACA;cAAA;gBAAA;gBAEAb;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IACA;IACAkE;MACA;MACA;IACA;IAEAC;MACA;QACA9F;QACAC;QACAC;QACAC;QACAC;MACA;MACA;IACA;IAEA;IACA2F;MACA;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MACA;MACA;IACA;IAEAC;MACA;MACA;MACA;IACA;IAEA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA;gBAAA,OAEA;cAAA;gBAAA5D;gBACA;kBACAC;gBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;EACA;EACA4D;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;gBACA;cACA;cACA;gBACA;kBACAzE;oBACAc;oBACAb;kBACA;gBACA;kBACA;gBACA;cAEA;cAEA;gBACA;cACA;cACA;cAAA;cAAA,OACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAyE;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAEA;cACA;cACA;cACA;;cAEA;cAAA;cAAA,OACA;YAAA;cAAA;cAAA,OAGA;YAAA;cAEA1E;gBACAC;gBACAa;gBACAyB;cACA;cAAA;cAAA;YAAA;cAAA;cAAA;cAEA1B;cACAb;gBACAC;gBACAa;cACA;YAAA;cAAA;cAEAd;cAAA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EAEA;EACA2E;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cACA;cACA;gBACAlE;cACA;gBACAI;gBACA;cACA;;cAEA;cACAb;gBACA;gBACA;gBACA;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACA4E;IACA5E;EACA;EACA6E;IACA7E;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC/mCA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/shifuIndex.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/shifuIndex.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./shifuIndex.vue?vue&type=template&id=5a79bfc8&scoped=true&\"\nvar renderjs\nimport script from \"./shifuIndex.vue?vue&type=script&lang=js&\"\nexport * from \"./shifuIndex.vue?vue&type=script&lang=js&\"\nimport style0 from \"./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"5a79bfc8\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/shifuIndex.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=template&id=5a79bfc8&scoped=true&\"", "var components\ntry {\n  components = {\n    uSwiper: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-swiper/u-swiper\" */ \"uview-ui/components/u-swiper/u-swiper.vue\"\n      )\n    },\n    uEmpty: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-empty/u-empty\" */ \"uview-ui/components/u-empty/u-empty.vue\"\n      )\n    },\n    uIcon: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-icon/u-icon\" */ \"uview-ui/components/u-icon/u-icon.vue\"\n      )\n    },\n    uModal: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-modal/u-modal\" */ \"uview-ui/components/u-modal/u-modal.vue\"\n      )\n    },\n    uLoadmore: function () {\n      return import(\n        /* webpackChunkName: \"node-modules/uview-ui/components/u-loadmore/u-loadmore\" */ \"uview-ui/components/u-loadmore/u-loadmore.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.list.length\n  var l0 = _vm.__map(_vm.list, function (item, index) {\n    var $orig = _vm.__get_orig(item)\n    var g1 = item.mobile.slice(0, 3)\n    return {\n      $orig: $orig,\n      g1: g1,\n    }\n  })\n  var g2 = _vm.list.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event) {\n      _vm.confirmshow = false\n    }\n    _vm.e1 = function ($event) {\n      _vm.masterModalShow = false\n    }\n    _vm.e2 = function ($event) {\n      _vm.detailModalShow = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n        l0: l0,\n        g2: g2,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=script&lang=js&\"", "\n<template>\n\t\n\t<view class=\"page\">\n\t\n\t\t<tabbar :cur=\"0\"></tabbar>\n\t\t<view class=\"img\">\n\t\t\t<u-swiper :list=\"list1\" height=\"108\"></u-swiper>\n\t\t</view>\n\t\t<!-- <view class=\"location-bar\">\n\t\t\t<view class=\"location-info\">\n\t\t\t\t<view class=\"location-text\">当前接单位置：{{ province + city + district || '定位中...' }}</view>\n\t\t\t</view>\n\t\t</view> -->\n\t\t<view class=\"tabs-container\">\n\t\t\t<view class=\"custom-tabs\">\n\t\t\t\t<view \n\t\t\t\t\tclass=\"tab-item\" \n\t\t\t\t\t:class=\"{ 'active': currentTab === index }\"\n\t\t\t\t\tv-for=\"(tab, index) in tabsList\" \n\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t@click=\"switchTab(index)\"\n\t\t\t\t>\n\t\t\t\t\t<text class=\"tab-text\">{{ tab.name }}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"filter-container\">\n\t\t\t<view class=\"filter-bar\">\n\t\t\t\t<view class=\"filter-item-container\">\n\t\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('price')\" :class=\"{'active-filter-item': isPriceFilterActive}\">\n\t\t\t\t\t\t<text>{{ priceFilterText }}</text>\n\t\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'price' }\">▼</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"filter-item-container\">\n\t\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('distance')\" :class=\"{'active-filter-item': isDistanceFilterActive}\">\n\t\t\t\t\t\t<text>订单距离 {{ distance }}公里</text>\n\t\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'distance' }\">▼</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t\n\t\t\t\t<view class=\"filter-item-container\">\n\t\t\t\t\t<view class=\"filter-item\" @click.stop=\"toggleFilter('category')\" :class=\"{'active-filter-item': isCategoryFilterActive}\">\n\t\t\t\t\t\t<text>{{ currentCateName || '分类筛选' }}</text>\n\t\t\t\t\t\t<text class=\"arrow\" :class=\"{ 'rotate': showFilter === 'category' }\">▼</text>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'price'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">价格范围</view>\n\t\t\t\t\t\t<view class=\"option-list\">\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 0 && priceRange.max === 100 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(0, 100, 'predefined')\"\n\t\t\t\t\t\t\t>0 - 100</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 100 && priceRange.max === 200 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(100, 200, 'predefined')\"\n\t\t\t\t\t\t\t>100 - 200</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 200 && priceRange.max === 500 && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(200, 500, 'predefined')\"\n\t\t\t\t\t\t\t>200 - 500</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t\t:class=\"{ active: priceRange.min === 500 && priceRange.max === null && priceRange.type === 'predefined' }\"\n\t\t\t\t\t\t\t\t@click=\"selectPriceRange(500, null, 'predefined')\"\n\t\t\t\t\t\t\t>500以上</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">自定义价格</view>\n\t\t\t\t\t\t<view class=\"custom-price-inputs\">\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\t\t\tv-model=\"priceRange.customMin\" \n\t\t\t\t\t\t\t\tplaceholder=\"最低价\" \n\t\t\t\t\t\t\t\t@blur=\"validatePriceInput('min')\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t<text>-</text>\n\t\t\t\t\t\t\t<input \n\t\t\t\t\t\t\t\ttype=\"digit\" \n\t\t\t\t\t\t\t\tv-model=\"priceRange.customMax\" \n\t\t\t\t\t\t\t\tplaceholder=\"最高价\" \n\t\t\t\t\t\t\t\t@blur=\"validatePriceInput('max')\"\n\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetPriceFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyPriceFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'distance'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">距离范围</view>\n\t\t\t\t\t\t<view class=\"distance-input\">\n\t\t\t\t\t\t\t<text>请输入距离范围（公里）</text>\n\t\t\t\t\t\t\t<view class=\"distance-input-container\">\n\t\t\t\t\t\t\t\t<input\n\t\t\t\t\t\t\t\t\ttype=\"digit\"\n\t\t\t\t\t\t\t\t\tv-model=\"distance\"\n\t\t\t\t\t\t\t\t\tplaceholder=\"请输入距离\"\n\t\t\t\t\t\t\t\t\t@blur=\"validateDistanceInput\"\n\t\t\t\t\t\t\t\t/>\n\t\t\t\t\t\t\t\t<text class=\"unit\">公里</text>\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t\t<view class=\"distance-hint\">范围：1-100公里</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetDistanceFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyDistanceFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t\n\t\t\t<view class=\"filter-dropdown\" v-if=\"showFilter === 'category'\" @click.stop>\n\t\t\t\t<view class=\"dropdown-content\">\n\t\t\t\t\t<view class=\"filter-section\">\n\t\t\t\t\t\t<view class=\"section-title\">服务品类</view>\n\t\t\t\t\t\t<view class=\"option-list\">\n\t\t\t\t\t\t\t<view class=\"option-item\" :class=\"{ active: currentCateId === '' }\" @click=\"selectClick({ id: '', name: '全部' })\">全部</view>\n\t\t\t\t\t\t\t<view \n\t\t\t\t\t\t\t\tclass=\"option-item\" \n\t\t\t\t\t\t\t\tv-for=\"(cate, index) in cateList\" \n\t\t\t\t\t\t\t\t:key=\"index\" \n\t\t\t\t\t\t\t\t:class=\"{ active: currentCateId === cate.id }\"\n\t\t\t\t\t\t\t\t@click=\"selectClick(cate)\"\n\t\t\t\t\t\t\t>\n\t\t\t\t\t\t\t\t{{ cate.name }}\n\t\t\t\t\t\t\t</view>\n\t\t\t\t\t\t</view>\n\t\t\t\t\t</view>\n\t\t\t\t\t\n\t\t\t\t\t<view class=\"filter-actions\">\n\t\t\t\t\t\t<view class=\"filter-btn reset\" @click=\"resetCategoryFilter\">重置</view>\n\t\t\t\t\t\t<view class=\"filter-btn confirm\" @click=\"applyCategoryFilter\">确定</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"quotation-counts\">\n\t\t\t<view class=\"counts-header\">\n\t\t\t\t<text class=\"counts-title\">接单统计</text>\n\t\t\t</view>\n\t\t\t<view class=\"counts-row\">\n\t\t\t\t<view @click=\"textclick\" class=\"count-item\">\n\t\t\t\t\t<text class=\"count-label\">比价订单</text>\n\t\t\t\t\t<text class=\"count-value\">{{QuotationCounts.comparisonOrder || 0}}/{{QuotationCounts.nowComparisonOrder || 0}}</text>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"count-divider\"></view>\n\t\t\t\t<view class=\"count-item\">\n\t\t\t\t\t<text class=\"count-label\">一口价订单</text>\n\t\t\t\t\t<text class=\"count-value\">{{QuotationCounts.fixedPrice || 0}}/{{QuotationCounts.nowFixedPrice || 0}}</text>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t</view>\n\t\t<view class=\"check_box\" v-if=\"false\">\n\t\t</view>\n\t\t<u-empty mode=\"order\" icon=\"http://cdn.uviewuni.com/uview/empty/order.png\" v-if=\"list.length == 0\"></u-empty>\n\t\t<view class=\"re_item\" v-for=\"(item, index) in list\" :key=\"index\" @click=\"seeDetail(item)\">\n\t\t\t<view class=\"top\">\n\t\t\t\t<image :src=\"item.goodsCover\" style=\"width: 160rpx;height: 160rpx;border-radius: 10rpx;\"></image>\n\t\t\t\t<view class=\"order\">\n\t\t\t\t\t<div class=\"title\">{{ item.goodsName }}<span v-if=\"item.type != 0\"\n\t\t\t\t\t\t\tstyle=\"font-size: 24rpx;color:#999;margin-left: 10rpx;\">(报价0.00元起)</span></div>\n\t\t\t\t\t<div class=\"price\">{{ item.type == 0 ? `￥${item.payPrice}` : '待报价' }}</div>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view @click=\"dingyue()\" class=\"info\">\n\t\t\t\t<view class=\"address\">\n\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t<u-icon name=\"map-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right\">\n\t\t\t\t\t\t<view class=\"address_name\">{{ item.address }}</view>\n\t\t\t\t\t\t<view class=\"address_Info\">{{ item.addressInfo }}</view>\n\t\t\t\t\t</view>\n\t\t\t\t</view>\n\t\t\t\t<view class=\"tel\">\n\t\t\t\t\t<view class=\"left\">\n\t\t\t\t\t\t<u-icon name=\"phone-fill\" color=\"#2979ff\" size=\"22\"></u-icon>\n\t\t\t\t\t</view>\n\t\t\t\t\t<view class=\"right\">{{ item.mobile.slice(0, 3) + '********' }}</view>\n\t\t\t\t</view>\n\t\t\t</view>\n\t\t\t<view class=\"notes\" v-if=\"item.text != ''\">\n\t\t\t\t<view style=\"color:#999999;\">备注内容:</view>\n\t\t\t\t{{ item.text }}\n\t\t\t</view>\n\t\t\t<view class=\"btn\" :style=\"item.type == 1 ? '' : 'background-color:#2E80FE;color:#fff;'\"\n\t\t\t\************=\"seeDetail(item)\">\n\t\t\t\t{{ item.type == 1 ? '立即报价' : '立即接单' }}\n\t\t\t</view>\n\t\t</view>\n\t\t<u-modal :show=\"confirmshow\" :content=\"content\" showCancelButton @confirm=\"confirmRe\"\n\t\t\t@cancel=\"confirmshow = false\"></u-modal>\n\n\t\t<u-modal :show=\"masterModalShow\" content=\"您还不是师傅,请去入驻\" showCancelButton @confirm=\"goToSettle\"\n\t\t\t@cancel=\"masterModalShow = false\"></u-modal>\n\n\t\t<u-modal :show=\"detailModalShow\" title=\"服务承诺\" showCancelButton cancelText=\"不同意\" confirmText=\"同意\"\n\t\t\t@confirm=\"confirmDetail\" @cancel=\"detailModalShow = false\" v-if=\"shifustutus.data !== -2 && shifustutus.data !== -1\">\n\t\t\t<view class=\"modal-content\">\n\t\t\t\t<rich-text\n\t\t\t\t\t:nodes=\"getconfigs?getconfigs:configInfo.shifuQualityCommitment\"></rich-text>\n\t\t\t</view>\n\t\t</u-modal>\n\n\t\t<view class=\"loadmore\" v-if=\"list.length >= 10\">\n\t\t\t<u-loadmore :status=\"status\" />\n\t\t</view>\n\t</view>\n</template>\n\n<script>\n\timport tabbar from \"@/components/tabbarsf.vue\";\n\timport {\n\t\tmapState,\n\t\tmapActions\n\t} from 'vuex';\n\texport default {\n\t\tcomponents: {\n\t\t\ttabbar\n\t\t},\n\t\tdata() {\n\t\t\treturn {\n\t\t\t\torderData: '',\n\t\t\t\tshowDingyue: false,\n\t\t\t\tshowCate: false, // This seems redundant with the new filter dropdown, consider removal\n\t\t\t\tinfodata: '',\n\t\t\t\ttmplIds: [\n\t\t\t\t\t' vR1qJM-SEYbGnvXdl4HQ5D2Nf7USnBgcmeov8slExOo',\n\t\t\t\t\t'HVNlAWjUm-wjtFxYizNdqzPvrYvofmysaXs_iZ0T1Gs',\n\t\t\t\t\t'iD-jH6RYVcTr-KDBlH8w7ZTQOSEPeXh02Z9pkvWq5JY'\n\t\t\t\t],\n\t\t\t\tstatus: 'loadmore',\n\t\t\t\tid: '',\n\t\t\t\tshifuId: '',\n\t\t\t\t// 新增的选项卡数据\n\t\t\t\tcurrentTab: 0,\n\t\t\t\ttabsList: [\n\t\t\t\t\t{ name: '聚合订单', badge: '' },\n\t\t\t\t\t{ name: '一口价', badge: '5' },\n\t\t\t\t\t{ name: '报价订单', badge: '11' },\n\t\t\t\t\t{ name: '高价值', badge: '' }\n\t\t\t\t],\n\t\t\t\tlistType:[{name:'一口价'},{name:'报价订单'},{name:'高价值 '}],\n\t\t\t\tlist: [],\n\t\t\t\tconfirmshow: false,\n\t\t\t\tmasterModalShow: false,\n\t\t\t\tdetailModalShow: false,\n\t\t\t\tcontent: '确认接下该订单吗',\n\t\t\t\tinput: '',\n\t\t\t\tarea_id: '',\n\t\t\t\tlimit: 10, // Changed to 10 for more realistic pagination in examples\n\t\t\t\tpage: 1,\n\t\t\t\tbannerList: [],\n\t\t\t\tconfigInfo: '',\n\t\t\t\tgetconfigs: '',\n\t\t\t\tlist1: [],\n\t\t\t\tlng: '',\n\t\t\t\tshifustutus: {\n\t\t\t\t\tdata: 0,\n\t\t\t\t\tmsg: ''\n\t\t\t\t},\n\t\t\t\tQuotationCounts:'',\n\t\t\t\tlat: '',\n\t\t\t\tcateList: [],\n\t\t\t\tcurrentCateId: '',\n\t\t\t\tcurrentCateName: '分类筛选', // Changed default to '分类筛选' for consistency\n\t\t\t\tcopyCateList: [],\n\t\t\t\tprovince: '',\n\t\t\t\tcity: '',\n\t\t\t\tdistrict: '',\n\t\t\t\tisPageLoaded: false,\n\t\t\t\tselectedItem: null,\n\t\t\t\t// 新增的数据\n\t\t\t\tpriceRange: {\n\t\t\t\t\ttype: 'all', // 'all', 'predefined', 'custom'\n\t\t\t\t\tmin: null,\n\t\t\t\t\tmax: null,\n\t\t\t\t\tcustomMin: '',\n\t\t\t\t\tcustomMax: ''\n\t\t\t\t},\n\t\t\t\tdistance: 20, // 默认20公里\n\t\t\t\tappliedDistance: 20, // To track applied distance for filter bar text\n\t\t\t\t// 折叠面板状态控制 - These seem unused with the new filter dropdown, consider removal\n\t\t\t\tisPriceCollapsed: true,\n\t\t\t\tisDistanceCollapsed: true,\n\t\t\t\tisCategoryCollapsed: true,\n\t\t\t\t// 筛选相关\n\t\t\t\tshowFilter: null,\n\t\t\t\tcurrentActivityType: null,\n\n\t\t\t\tareaCount: 0\n\t\t\t};\n\t\t},\n\t\tcomputed: {\n\t\t\t...mapState({\n\t\t\t\tconfigInfos: (state) => state.config.configInfo,\n\t\t\t\t\tregeocode: (state) => state.service.regeocode,\n\t\t\t\trefreshReceiving: (state) => state.service.refreshReceiving || '',\n\t\t\t}),\n\t\t\tpriceFilterText() {\n\t\t\t\tif (this.priceRange.type === 'all' || (this.priceRange.customMin === '' && this.priceRange.customMax === '')) {\n\t\t\t\t\treturn '价格区间';\n\t\t\t\t} else if (this.priceRange.type === 'predefined') {\n\t\t\t\t\treturn `${this.priceRange.min} - ${this.priceRange.max === null ? '500以上' : this.priceRange.max}`;\n\t\t\t\t} else if (this.priceRange.type === 'custom') {\n\t\t\t\t\tlet min = this.priceRange.customMin || '最低价';\n\t\t\t\t\tlet max = this.priceRange.customMax || '最高价';\n\t\t\t\t\tif (this.priceRange.customMin && this.priceRange.customMax) {\n\t\t\t\t\t\treturn `${min} - ${max}`;\n\t\t\t\t\t} else if (this.priceRange.customMin) {\n\t\t\t\t\t\treturn `${min}以上`;\n\t\t\t\t\t} else if (this.priceRange.customMax) {\n\t\t\t\t\t\treturn `${max}以下`;\n\t\t\t\t\t}\n\t\t\t\t\treturn '自定义价格'; // Fallback\n\t\t\t\t}\n\t\t\t\treturn '价格区间';\n\t\t\t},\n\t\t\tisPriceFilterActive() {\n\t\t\t\treturn this.priceRange.type !== 'all';\n\t\t\t},\n\t\t\tisDistanceFilterActive() {\n\t\t\t\treturn this.distance !== 20; // Assuming 20km is the default/reset state\n\t\t\t},\n\t\t\tisCategoryFilterActive() {\n\t\t\t\treturn this.currentCateId !== ''; // Assuming empty string is the default/reset state for '全部'\n\t\t\t}\n\t\t},\n\t\tmethods: {\n\t\t\ttextclick(){\n\t\t\t\tthis.$api.shifu.gettext()\n\t\t\t},\n\t\t\t// 新增的选项卡切换方法\n\t\t\tswitchTab(index) {\n\t\t\t\tthis.currentTab = index;\n\t\t\t\t// 根据选项卡切换不同的数据\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.list = []; // Clear list on tab switch\n\t\t\t\tthis.getListByTab(index);\n\t\t\t\tthis.closeFilter(); // Close any open filter dropdown\n\t\t\t},\n\t\t\t\n\t\t\t// Helper to get order type based on current tab\n\t\t\tgetOrderType(tabIndex) {\n\t\t\t\tswitch(tabIndex) {\n\t\t\t\t\tcase 0: return undefined; // 聚合订单 - no type\n\t\t\t\t\tcase 1: return 0; // 一口价\n\t\t\t\t\tcase 2: return 1; // 报价订单\n\t\t\t\t\tcase 3: return undefined; // 高价值\n\t\t\t\t\tdefault: return undefined;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// Helper to get menu type based on current tab\n\t\t\tgetMenuType(tabIndex) {\n\t\t\t\tswitch(tabIndex) {\n\t\t\t\t\tcase 3: return 2; // 高价值\n\t\t\t\t\tdefault: return 1; // 其他tab\n\t\t\t\t}\n\t\t\t},\n\n\t\t\t// 根据选项卡获取不同的数据\n\t\t\tasync getListByTab(tabIndex) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\tlet apiParams = {\n\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\t\ttype: this.getOrderType(tabIndex), // Set type based on tab\n\t\t\t\t\t\tmenu: this.getMenuType(tabIndex), // Set menu based on tab\n\t\t\t\t\t\tdistance: this.appliedDistance, // Use appliedDistance\n\t\t\t\t\t\tminPrice: this.priceRange.type !== 'all' ? this.priceRange.min : undefined,\n\t\t\t\t\t\tmaxPrice: this.priceRange.type !== 'all' ? this.priceRange.max : undefined,\n\t\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t\t};\n\n\t\t\t\t\t// Clean up undefined values from apiParams\n\t\t\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\t\t\t\t\t\n\t\t\t\t\tconst res = await this.$api.shifu.indexQuote(apiParams);\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t}, 3000);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.$set(this, 'list', res.data.list || []);\n\t\t\t\t\tlet count = this.list.length;\n\t\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\t\tthis.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Error fetching list by tab:\", error);\n\t\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\treset() {\n\t\t\t\tthis.currentCateName = '分类筛选';\n\t\t\t\tthis.currentCateId = '';\n\t\t\t\tthis.priceRange = {\n\t\t\t\t\ttype: 'all',\n\t\t\t\t\tmin: null,\n\t\t\t\t\tmax: null,\n\t\t\t\t\tcustomMin: '',\n\t\t\t\t\tcustomMax: ''\n\t\t\t\t};\n\t\t\t\tthis.distance = 20;\n\t\t\t\tthis.appliedDistance = 20;\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.getListByTab(this.currentTab);\n\t\t\t},\n\t\t\t\n\t\t\t// This closeCate method seems redundant if using the new filter dropdown system\n\t\t\tcloseCate() {\n\t\t\t\tthis.showCate = false;\n\t\t\t\tsetTimeout(() => {\n\t\t\t\t\tthis.cateList = this.copyCateList;\n\t\t\t\t}, 500);\n\t\t\t},\n\t\t\t// This chooseCate method seems redundant if using the new filter dropdown system\n\t\t\tchooseCate() {\n\t\t\t\tthis.showCate = !this.showCate;\n\t\t\t},\n\t\t\t\n\t\t\tasync selectClick(cate) {\n\t\t\t\t// Only update the selected category without immediately applying the filter\n\t\t\t\t// The filter will be applied when '确定' is clicked in the category dropdown\n\t\t\t\tthis.currentCateName = cate.name;\n\t\t\t\tthis.currentCateId = cate.id;\n\t\t\t},\n\t\t\tasync getCate() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.serviceCate();\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.cateList = res || [];\n\t\t\t\t\tthis.copyCateList = res || []; // Keep a copy if you need to reset the list later\n\t\t\t\t} catch (error) {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '获取分类失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tasync seeDetail(item) {\n\t\t\t\t// Fetch shifu status before opening any modal\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.getshifstutas({\n\t\t\t\t\t\t\t\n\t\t\t\t\tuserId:uni.getStorageSync('userId')\n\t\t\t\t});\n\t\t\t\t\tthis.shifustutus = res;\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.selectedItem = item;\n\t\t\t\t\tif (this.shifustutus.data === -1) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: this.shifustutus.msg || '无法进行此操作'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.detailModalShow = true;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Error checking shifu status:\", error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '检查身份失败'\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirmDetail() {\n\t\t\t\tif (this.selectedItem) {\n\t\t\t\t\tconsole.log(this.selectedItem)\n\t\t\t\t\tuni.setStorageSync('selectedOrder', this.selectedItem);\n\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\turl: `/shifu/master_order_details?id=${this.selectedItem.id}&goodsId=${this.selectedItem.goodsId}&type=${this.selectedItem.type}`\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t\tthis.detailModalShow = false;\n\t\t\t\tthis.selectedItem = null;\n\t\t\t},\n\t\t\tasync getList() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\tlet location = {\n\t\t\t\t\t\tlongitude: '',\n\t\t\t\t\t\tlatitude: ''\n\t\t\t\t\t};\n\t\t\t\t\ttry {\n\t\t\t\t\t\t// Add a small delay if needed for getLocation, but usually not\n\t\t\t\t\t\tlocation = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.getLocation({\n\t\t\t\t\t\t\t\ttype: 'gcj02',\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.lng = location.longitude;\n\t\t\t\t\t\tthis.lat = location.latitude;\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '定位失败，使用默认位置'\n\t\t\t\t\t\t});\n\t\t\t\t\t}\n\t\t\t\t\ttry {\n\t\t\t\t\t\tconst geoRes = await new Promise((resolve, reject) => {\n\t\t\t\t\t\t\tuni.request({\n\t\t\t\t\t\t\t\turl: `https://restapi.amap.com/v3/geocode/regeo?location=${this.lng},${this.lat}&key=2fb9ec1a184338e3cce567b7d2bab08f`,\n\t\t\t\t\t\t\t\tsuccess: resolve,\n\t\t\t\t\t\t\t\tfail: reject\n\t\t\t\t\t\t\t});\n\t\t\t\t\t\t});\n\t\t\t\t\t\tthis.$store.dispatch('setRegeocode', {\n\t\t\t\t\t\t                                regeocode: geoRes.data.regeocode,\n\t\t\t\t\t\t                                lat: this.lat,\n\t\t\t\t\t\t                                lng: this.lng\n\t\t\t\t\t\t                            });\n\t\t\t\t\t\tconsole.log(geoRes.data)\n\t\t\t\t\t\t// 设置页面显示的地理位置信息\n\t\t\t\t\t\tthis.province = geoRes.data.regeocode.addressComponent.province || '';\n\t\t\t\t\t\tthis.city = geoRes.data.regeocode.addressComponent.city || '';\n\t\t\t\t\t\tthis.district = geoRes.data.regeocode.addressComponent.district || '';\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存地理位置数据到本地存储，供登录时使用\n\t\t\t\t\t\tconst locationData = {\n\t\t\t\t\t\t\tprovince: this.province,\n\t\t\t\t\t\t\tcity: this.city,\n\t\t\t\t\t\t\tcounty: this.district, // district对应county\n\t\t\t\t\t\t\taddress: geoRes.data.regeocode.formatted_address || '',\n\t\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t\t\tlat: this.lat\n\t\t\t\t\t\t};\n\t\t\t\t\t\tuni.setStorageSync('locationData', locationData);\n\t\t\t\t\t} catch (error) {\n\t\t\t\t\t\tthis.province = '安徽省'; // Default province\n\t\t\t\t\t\tthis.city = '阜阳市'; // Default city\n\t\t\t\t\t\tthis.district = '临泉县'; // Default district\n\t\t\t\t\t\t\n\t\t\t\t\t\t// 保存默认地理位置数据\n\t\t\t\t\t\tconst defaultLocationData = {\n\t\t\t\t\t\t\tprovince: this.province,\n\t\t\t\t\t\t\tcity: this.city,\n\t\t\t\t\t\t\tcounty: this.district,\n\t\t\t\t\t\t\taddress: `${this.province}${this.city}${this.district}`,\n\t\t\t\t\t\t\tlng: this.lng || '',\n\t\t\t\t\t\t\tlat: this.lat || ''\n\t\t\t\t\t\t};\n\t\t\t\t\t\tconsole.log(defaultLocationData)\n\t\t\t\t\t\tuni.setStorageSync('locationData', defaultLocationData);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t// Initial list fetch will correspond to the default tab (聚合订单, tabIndex 0)\n\t\t\t\t\tconst res = await this.$api.shifu.indexQuote({\n\t\t\t\t\t\tlng: this.lng,\n\t\t\t\t\t\tlat: this.lat,\n\t\t\t\t\t\tparentId: 0,\n\t\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\t\tdistance: this.appliedDistance, // Ensure initial load uses appliedDistance\n\t\t\t\t\t\tmenu: this.getMenuType(this.currentTab), // Include menu for initial load\n\t\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t\t\t// No type parameter for initial聚合订单 load\n\t\t\t\t\t});\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t}, 3000);\n\t\t\t\t\t}\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.$set(this, 'list', res.data.list || []);\n\t\t\t\t\tlet count = this.list.length;\n\t\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\t\tthis.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Error fetching initial list:\", error);\n\t\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\thandleReceive(item) {\n\t\t\t\t// this.textsss(); // Assuming this is defined elsewhere or not critical\n\t\t\t\tthis.orderData = item;\n\t\t\t\tthis.id = item.id;\n\t\t\t\tif (item.type == 0) { // Assuming type 0 means fixed price that can be directly received\n\t\t\t\t\tthis.confirmshow = true;\n\t\t\t\t}\n\t\t\t},\n\t\t\tconfirmRe() {\n\t\t\t\tthis.confirmshow = false;\n\t\t\t\tthis.$api.shifu.rece_Order({\n\t\t\t\t\torder_id: this.id\n\t\t\t\t}).then(res => {\n\t\t\t\t\tthis.getList();\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'success',\n\t\t\t\t\t\ttitle: '接单成功',\n\t\t\t\t\t\tduration: 1000\n\t\t\t\t\t});\n\t\t\t\t\tsetTimeout(() => {\n\t\t\t\t\t\tuni.navigateTo({\n\t\t\t\t\t\t\turl: '/shifu/master_my_order'\n\t\t\t\t\t\t});\n\t\t\t\t\t}, 1000);\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'fail',\n\t\t\t\t\t\ttitle: error.message || '接单失败'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tgoToSettle() {\n\t\t\t\tthis.masterModalShow = false;\n\t\t\t\tuni.navigateTo({\n\t\t\t\t\turl: '/shifu/Settle'\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync getServiceInfo() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.index({\n\t\t\t\t\t\tcity_id: this.area_id\n\t\t\t\t\t});\n\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tthis.bannerList = res.data || [];\n\t\t\t\t\tthis.list1 = res.data.map(item => item.img) || [];\n\t\t\t\t\tif (!this.list1.length) {\n\t\t\t\t\t\tthis.list1 = [\n\t\t\t\t\t\t\t'https://zskj.asia/attachment/image/666/24/09/2bdd13fab41b42b987bcfc501aa535bb.jpg'\n\t\t\t\t\t\t];\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\t// uni.showToast({\n\t\t\t\t\t// \ticon: 'none',\n\t\t\t\t\t// \ttitle: '获取轮播图失败'\n\t\t\t\t\t// });\n\t\t\t\t\tthis.list1 = ['https://zskj.asia/attachment/image/666/24/09/e790eea3f21b4f48ab2b00b034468035.jpg'];\n\t\t\t\t}\n\t\t\t},\n\t\t\tonReachBottom() {\n\t\t\t\tif (this.status == 'nomore') return;\n\t\t\t\tthis.status = 'loading';\n\t\t\t\tthis.page++;\n\t\t\t\tlet apiParams = {\n\t\t\t\t\tpageNum: this.page,\n\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\tlat: this.lat,\n\t\t\t\t\tlng: this.lng,\n\t\t\t\t\tdistance: this.appliedDistance, // Include appliedDistance for pagination\n\t\t\t\t\tminPrice: this.priceRange.type !== 'all' ? this.priceRange.min : undefined,\n\t\t\t\t\tmaxPrice: this.priceRange.type !== 'all' ? this.priceRange.max : undefined,\n\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t};\n\n\t\t\t\t// Apply the 'type' and 'menu' parameter based on the current tab for pagination\n\t\t\t\tapiParams.type = this.getOrderType(this.currentTab);\n\t\t\t\tapiParams.menu = this.getMenuType(this.currentTab);\n\t\t\t\t\n\t\t\t\t// Clean up undefined values from apiParams\n\t\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\t\tthis.$api.shifu.indexQuote(apiParams).then(res => {\n\t\t\t\t\tif (!res.data || !res.data.list || res.data.list.length === 0) {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: '没有更多数据了'\n\t\t\t\t\t\t});\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t\tthis.$set(this, 'list', [...this.list, ...(res.data.list || [])]);\n\t\t\t\t\tif (res.data.list.length < this.limit) {\n\t\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.status = 'loadmore';\n\t\t\t\t\t}\n\t\t\t\t}).catch(error => {\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '加载失败，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t});\n\t\t\t},\n\t\t\tasync initializePage() {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '初始化中'\n\t\t\t\t});\n\t\t\t\ttry {\n\t\t\t\t\t// const systemInfo = uni.getSystemInfoSync(); // This line is not used\n\t\t\t\t\tawait this.getServiceInfo();\n\t\t\t\t\tawait this.getCate();\n\t\t\t\t\tawait this.getList(); // This will load the '聚合订单' initially\n\t\t\t\t\n\t\t\t\t\t// Get userId from storage if available\n\t\t\t\t\tif (uni.getStorageSync('shiInfo')) {\n\t\t\t\t\t\tthis.infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.configInfo = uni.getStorageSync('configInfo');\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Error initializing page:\", error);\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '初始化失败，请稍后重试'\n\t\t\t\t\t});\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 新增的方法\n\t\t\tselectPriceRange(min, max, type) {\n\t\t\t\tthis.priceRange = {\n\t\t\t\t\ttype: type,\n\t\t\t\t\tmin: min,\n\t\t\t\t\tmax: max,\n\t\t\t\t\tcustomMin: min !== null ? min.toString() : '',\n\t\t\t\t\tcustomMax: max !== null ? max.toString() : ''\n\t\t\t\t};\n\t\t\t},\n\t\t\t\n\t\t\tvalidatePriceInput(type) {\n\t\t\t\tif (type === 'min') {\n\t\t\t\t\tconst min = parseFloat(this.priceRange.customMin);\n\t\t\t\t\tif (!isNaN(min) && min >= 0) {\n\t\t\t\t\t\tthis.priceRange.min = min;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.priceRange.customMin = '';\n\t\t\t\t\t\tthis.priceRange.min = null;\n\t\t\t\t\t}\n\t\t\t\t} else if (type === 'max') {\n\t\t\t\t\tconst max = parseFloat(this.priceRange.customMax);\n\t\t\t\t\tif (!isNaN(max) && max > 0) {\n\t\t\t\t\t\tthis.priceRange.max = max;\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.priceRange.customMax = '';\n\t\t\t\t\t\tthis.priceRange.max = null;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t// Determine priceRange.type based on custom inputs or predefined selection\n\t\t\t\tconst hasCustomMin = this.priceRange.customMin !== '';\n\t\t\t\tconst hasCustomMax = this.priceRange.customMax !== '';\n\t\t\t\n\t\t\t\tif (hasCustomMin || hasCustomMax) {\n\t\t\t\t\tthis.priceRange.type = 'custom';\n\t\t\t\n\t\t\t\t\t// Check if custom inputs match any predefined range for highlighting\n\t\t\t\t\tconst predefinedRanges = [\n\t\t\t\t\t\t{ min: 0, max: 100 },\n\t\t\t\t\t\t{ min: 100, max: 200 },\n\t\t\t\t\t\t{ min: 200, max: 500 },\n\t\t\t\t\t\t{ min: 500, max: null }\n\t\t\t\t\t];\n\t\t\t\t\tlet matchedPredefined = false;\n\t\t\t\t\tfor (const range of predefinedRanges) {\n\t\t\t\t\t\tif (this.priceRange.min === range.min && this.priceRange.max === range.max) {\n\t\t\t\t\t\t\tthis.priceRange.type = 'predefined';\n\t\t\t\t\t\t\tmatchedPredefined = true;\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t\tif (!matchedPredefined && (hasCustomMin || hasCustomMax)) {\n\t\t\t\t\t\tthis.priceRange.type = 'custom';\n\t\t\t\t\t}\n\t\t\t\t} else if (!this.priceRange.min && !this.priceRange.max) {\n\t\t\t\t\tthis.priceRange.type = 'all';\n\t\t\t\t} else if (this.priceRange.type !== 'predefined') {\n\t\t\t\t\t// Fallback if somehow min/max are set but custom inputs are empty and not predefined\n\t\t\t\t\tthis.priceRange.type = 'custom';\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\tvalidateDistanceInput() {\n\t\t\t\tconst distance = parseFloat(this.distance);\n\t\t\t\tif (isNaN(distance) || distance < 1) {\n\t\t\t\t\tthis.distance = 1;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '距离不能小于1公里'\n\t\t\t\t\t});\n\t\t\t\t} else if (distance > 100) {\n\t\t\t\t\tthis.distance = 100;\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: '距离不能大于100公里'\n\t\t\t\t\t});\n\t\t\t\t} else {\n\t\t\t\t\tthis.distance = Math.round(distance);\n\t\t\t\t}\n\t\t\t},\n\t\t\t\n\t\t\t// This applyFilters method is a general one; the specific apply methods (price, distance, category) will call fetchFilteredData\n\t\t\tapplyFilters() {\n\t\t\t\t// This method can be used if there was a single \"Apply All Filters\" button.\n\t\t\t\t// Since we have separate \"确定\" buttons for each dropdown, we will use\n\t\t\t\t// applyPriceFilter, applyDistanceFilter, applyCategoryFilter.\n\t\t\t\t// For now, it will just call the common fetchFilteredData with current state.\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\n\t\t\t// Adding a helper to build common API parameters\n\t\t\tbuildFilterParams() {\n\t\t\t\tlet apiParams = {\n\t\t\t\t\tlng: this.lng,\n\t\t\t\t\tlat: this.lat,\n\t\t\t\t\tpageNum: 1, // Always reset to page 1 on new filter application\n\t\t\t\t\tpageSize: this.limit,\n\t\t\t\t\tparentId: this.currentCateId || 0,\n\t\t\t\t\tdistance: this.appliedDistance,\n\t\t\t\t\tuserId: this.infodata.userId, // Assuming userId is available in infodata\n\t\t\t\t\tquotationNum: true // As per requirement\n\t\t\t\t};\n\t\t\t\t\n\t\t\t\t// Add price range parameters\n\t\t\t\tif (this.priceRange.type !== 'all') {\n\t\t\t\t\tapiParams.minPrice = this.priceRange.min;\n\t\t\t\t\tapiParams.maxPrice = this.priceRange.max;\n\t\t\t\t}\n\n\t\t\t\t// Set type and menu based on the current active tab\n\t\t\t\tapiParams.type = this.getOrderType(this.currentTab);\n\t\t\t\tapiParams.menu = this.getMenuType(this.currentTab);\n\t\t\t\t\n\t\t\t\t// Clean up undefined values from apiParams\n\t\t\t\tObject.keys(apiParams).forEach(key => apiParams[key] === undefined && delete apiParams[key]);\n\n\t\t\t\treturn apiParams;\n\t\t\t},\n\t\t\t\n\t\t\t// Adding collapse functionality (if still needed, though current UI uses show/hide)\n\t\t\ttoggleCollapse(type) {\n\t\t\t\tif (type === 'price') {\n\t\t\t\t\tthis.isPriceCollapsed = !this.isPriceCollapsed;\n\t\t\t\t} else if (type === 'distance') {\n\t\t\t\t\tthis.isDistanceCollapsed = !this.isDistanceCollapsed;\n\t\t\t\t} else if (type === 'category') {\n\t\t\t\t\tthis.isCategoryCollapsed = !this.isCategoryCollapsed;\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 筛选相关方法\n\t\t\ttoggleFilter(filter) {\n\t\t\t\tif (this.showFilter === filter) {\n\t\t\t\t\tthis.showFilter = null;\n\t\t\t\t} else {\n\t\t\t\t\tthis.showFilter = filter;\n\t\t\t\t}\n\t\t\t},\n\n\t\t\tcloseFilter() {\n\t\t\t\tthis.showFilter = null;\n\t\t\t},\n\t\t\t// These activity, area, and sort related methods seem to be remnants and aren't tied to the current UI\n\t\t\t// They should be removed if not implemented in the template.\n\t\t\tselectActivityType(item) {\n\t\t\t\tthis.currentActivityType = item.value;\n\t\t\t},\n\t\t\tapplyActivityFilter() {\n\t\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\t\tif (this.currentActivityType) {\n\t\t\t\t\tapiParams.activityType = this.currentActivityType;\n\t\t\t\t}\n\t\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\tresetActivityFilter() {\n\t\t\t\tthis.currentActivityType = '';\n\t\t\t},\n\t\t\tselectArea(item) {\n\t\t\t\tthis.currentArea = item.name;\n\t\t\t},\n\t\t\tapplyAreaFilter() {\n\t\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\t\tif (this.currentArea) {\n\t\t\t\t\tconst selectedArea = this.areaList.find(item => item.name === this.currentArea);\n\t\t\t\t\tif (selectedArea) {\n\t\t\t\t\t\tapiParams.area = selectedArea.value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\tresetAreaFilter() {\n\t\t\t\tthis.currentArea = '';\n\t\t\t},\n\t\t\tselectSort(item) {\n\t\t\t\tthis.currentSort = item.name;\n\t\t\t},\n\t\t\tapplySortFilter() {\n\t\t\t\tlet apiParams = this.buildFilterParams();\n\t\t\t\tif (this.currentSort) {\n\t\t\t\t\tconst selectedSort = this.sortOptions.find(item => item.name === this.currentSort);\n\t\t\t\t\tif (selectedSort) {\n\t\t\t\t\t\tapiParams.sort = selectedSort.value;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\tthis.fetchFilteredData(apiParams);\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\tresetSortFilter() {\n\t\t\t\tthis.currentSort = '';\n\t\t\t},\n\t\t\t\n\t\t\tapplyAdvancedFilter() {\n\t\t\t\t// This method is redundant if filters are applied individually.\n\t\t\t\t// If it's for a \"master\" apply button, it would call fetchFilteredData with current state.\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\t\n\t\t\tresetAdvancedFilter() {\n\t\t\t\tthis.priceRange = {\n\t\t\t\t\ttype: 'all',\n\t\t\t\t\tmin: null,\n\t\t\t\t\tmax: null,\n\t\t\t\t\tcustomMin: '',\n\t\t\t\t\tcustomMax: ''\n\t\t\t\t};\n\t\t\t\tthis.distance = 20;\n\t\t\t\tthis.appliedDistance = 20;\n\t\t\t\tthis.currentCateId = '';\n\t\t\t\tthis.currentCateName = '分类筛选';\n\t\t\t\tthis.page = 1; // Reset page on full reset\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams()); // Apply reset\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\t\n\t\t\t// 统一的数据获取方法\n\t\t\tasync fetchFilteredData(apiParams) {\n\t\t\t\tuni.showLoading({\n\t\t\t\t\ttitle: '加载中'\n\t\t\t\t});\n\t\t\t\tthis.list = []; // Clear list before fetching new data\n\t\t\t\tthis.page = 1; // Ensure page is reset when new filters are applied\n\n\t\t\t\t// Ensure userId is consistently added if infodata is available\n\t\t\t\t// if (this.infodata && this.infodata.userId) {\n\t\t\t\t// \tapiParams.userId = this.infodata.userId;\n\t\t\t\t// }\n\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.indexQuote(apiParams);\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\t\tif (res.code === \"-1\") {\n\t\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t\t}, 3000);\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\tthis.$set(this, 'list', res.data.list || []);\n\t\t\t\t\tlet count = this.list.length;\n\t\t\t\t\tuni.setStorageSync('listCount', count);\n\t\t\t\t\tthis.status = (res.data.list && res.data.list.length < this.limit) ? 'nomore' : 'loadmore';\n\t\t\t\t\tthis.$forceUpdate();\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error(\"Error fetching filtered data:\", error);\n\t\t\t\t\tthis.$set(this, 'list', []);\n\t\t\t\t\tthis.status = 'nomore';\n\t\t\t\t} finally {\n\t\t\t\t\tuni.hideLoading();\n\t\t\t\t}\n\t\t\t},\n\t\t\t// 价格筛选相关方法\n\t\t\tapplyPriceFilter() {\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\t\n\t\t\tresetPriceFilter() {\n\t\t\t\tthis.priceRange = {\n\t\t\t\t\ttype: 'all',\n\t\t\t\t\tmin: null,\n\t\t\t\t\tmax: null,\n\t\t\t\t\tcustomMin: '',\n\t\t\t\t\tcustomMax: ''\n\t\t\t\t};\n\t\t\t\tthis.applyPriceFilter(); // Apply reset immediately\n\t\t\t},\n\t\t\t\n\t\t\t// 距离筛选相关方法\n\t\t\tapplyDistanceFilter() {\n\t\t\t\tthis.appliedDistance = this.distance; // Update applied distance\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\t\n\t\t\tresetDistanceFilter() {\n\t\t\t\tthis.distance = 20; // Reset slider value\n\t\t\t\tthis.appliedDistance = 20; // Reset applied value\n\t\t\t\tthis.applyDistanceFilter(); // Apply reset immediately\n\t\t\t},\n\t\t\t\n\t\t\t// 分类筛选相关方法\n\t\t\tapplyCategoryFilter() {\n\t\t\t\tthis.fetchFilteredData(this.buildFilterParams());\n\t\t\t\tthis.closeFilter();\n\t\t\t},\n\t\t\t\n\t\t\tresetCategoryFilter() {\n\t\t\t\tthis.currentCateId = '';\n\t\t\t\tthis.currentCateName = '分类筛选';\n\t\t\t\tthis.applyCategoryFilter(); // Apply reset immediately\n\t\t\t},\n\n\t\t\t// 获取接单统计数据\n\t\t\tasync getQuotationCounts() {\n\t\t\t\ttry {\n\t\t\t\t\tconst res = await this.$api.shifu.getQuotationCounts();\n\t\t\t\t\tif (res.code === '-1') {\n\t\t\t\t\t\tconsole.error('获取接单统计失败:', res.msg);\n\t\t\t\t\t} else {\n\t\t\t\t\t\tthis.QuotationCounts = res.data;\n\t\t\t\t\t}\n\t\t\t\t} catch (error) {\n\t\t\t\t\tconsole.error('获取接单统计失败:', error);\n\t\t\t\t}\n\t\t\t},\n\t\t},\n\t\tasync onLoad() {\n\t\t\tthis.$api.base.getConfig().then(res => {\n\t\t\t\tthis.getconfigs = res.data.shifuQualityCommitment\n\t\t\t})\n\t\tthis.$api.shifu.getQuotationCounts().then(res => {\n\t\t\t\tif(res.code==='-1'){\n\t\t\t\t\tuni.showToast({\n\t\t\t\t\t\ticon: 'none',\n\t\t\t\t\t\ttitle: res.msg\n\t\t\t\t\t}, 3000);\n\t\t\t\t}else{\n\t\t\t\t\tthis.QuotationCounts=res.data\n\t\t\t\t}\n\t\t\t\n\t\t});\n\t\t\n\t\t\tif (uni.getStorageSync('shiInfo')) {\n\t\t\t\tthis.infodata = JSON.parse(uni.getStorageSync('shiInfo'));\n\t\t\t}\n\t\t\tthis.isPageLoaded = true;\n\t\t\tawait this.initializePage();\n\t\t},\n\t\tasync onPullDownRefresh() {\n\t\t\ttry {\n\t\t\t\t// 重置页面状态\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.list = [];\n\t\t\t\tthis.status = 'loadmore';\n\n\t\t\t\t// 重新获取数据\n\t\t\t\tawait this.getListByTab(this.currentTab);\n\n\t\t\t\t// 刷新接单统计\n\t\t\t\tawait this.getQuotationCounts();\n\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新成功',\n\t\t\t\t\ticon: 'success',\n\t\t\t\t\tduration: 1000\n\t\t\t\t});\n\t\t\t} catch (error) {\n\t\t\t\tconsole.error('下拉刷新失败:', error);\n\t\t\t\tuni.showToast({\n\t\t\t\t\ttitle: '刷新失败，请重试',\n\t\t\t\t\ticon: 'none'\n\t\t\t\t});\n\t\t\t} finally {\n\t\t\t\tuni.stopPullDownRefresh();\n\t\t\t}\n\t\t},\n\t\tasync onShow() {\n\t\t\t// Ensure shifu status is checked on show if not already done, or if it needs to be refreshed\n\t\t\tthis.$api.shifu.getshifstutas({\n\t\t\t\t\tuserId:uni.getStorageSync('userId')\n\t\t\t\t}).then(res => {\n\t\t\t\t\t\tconsole.log(res)\n\t\t\t\tthis.shifustutus = res;\n\t\t\t});\n\n\t\t\t// Re-fetch list if refresh signal is received (e.g., from another page after an order action)\n\t\t\tuni.$on('refreshReceivingList', () => {\n\t\t\t\tthis.page = 1;\n\t\t\t\tthis.list = [];\n\t\t\t\tthis.getListByTab(this.currentTab);\n\t\t\t});\n\t\t},\n\t\tonHide() {\n\t\t\tuni.$off('refreshReceivingList'); // Unregister event listener when page hides\n\t\t},\n\t\tonUnload() {\n\t\t\tuni.$off('refreshReceivingList'); // Unregister event listener when page unloads\n\t\t}\n\t};\n</script>\n\n<style scoped lang=\"scss\">\n\t.page {\n\t\tmin-height: 100vh;\n\t\toverflow: auto;\n\t\tbackground-color: #f3f4f5;\n\t\tpadding-bottom: 120rpx;\n\n\t\t.img {\n\t\t\twidth: 690rpx;\n\t\t\tmargin: 20rpx auto;\n\t\t}\n\n\t\t.location-bar {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 20rpx;\n\t\t\tborder: 1rpx solid #eeeeee;\n\t\t\tcolor: #999;\n\t\t\tfont-size: 28rpx;\n\t\t\tbackground-color: #fff;\n\t\t}\n\n\t\t.subscription {\n\t\t\tflex-shrink: 0;\n\t\t}\n\n\t\t.location-info {\n\t\t\tflex: 1;\n\t\t\ttext-align: left;\n\n\t\t\t.location-text {\n\t\t\t\tmax-width: 100%;\n\t\t\t\toverflow: hidden;\n\t\t\t\twhite-space: nowrap;\n\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\tline-height: 1.4;\n\t\t\t}\n\t\t}\n\n\t\t// 新增的选项卡样式\n\t\t.tabs-container {\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 0 20rpx 20rpx 20rpx;\n\t\t\tborder-radius: 10rpx;\n\t\t\toverflow: hidden;\n\t\t}\n\n\t\t.custom-tabs {\n\t\t\tdisplay: flex;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-bottom: 1rpx solid #f0f0f0;\n\n\t\t\t.tab-item {\n\t\t\t\tflex: 1;\n\t\t\t\tpadding: 25rpx 10rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tposition: relative;\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-direction: column;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\n\t\t\t\t.tab-text {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\ttransition: color 0.3s ease;\n\t\t\t\t}\n\n\t\t\t\t.tab-badge {\n\t\t\t\t\tposition: absolute;\n\t\t\t\t\ttop: 10rpx;\n\t\t\t\t\tright: 15rpx;\n\t\t\t\t\tbackground-color: #ff4757;\n\t\t\t\t\tcolor: #fff;\n\t\t\t\t\tfont-size: 20rpx;\n\t\t\t\t\tpadding: 2rpx 8rpx;\n\t\t\t\t\tborder-radius: 12rpx;\n\t\t\t\t\tmin-width: 20rpx;\n\t\t\t\t\theight: 24rpx;\n\t\t\t\t\tline-height: 20rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t}\n\n\t\t\t\t&.active {\n\t\t\t\t\t.tab-text {\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tbottom: 0;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\twidth: 60rpx;\n\t\t\t\t\t\theight: 4rpx;\n\t\t\t\t\t\tbackground-color: #2E80FE;\n\t\t\t\t\t\tborder-radius: 2rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.filter-container {\n\t\t\tdisplay: flex;\n\t\t\tflex-direction: column;\n\t\t\tbackground-color: #fff;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.filter-bar {\n\t\t\tdisplay: flex;\n\t\t\tbackground-color: #fff;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t\tposition: relative;\n\t\t\tz-index: 10;\n\t\t}\n\n\t\t.filter-item-container {\n\t\t\tflex: 1;\n\t\t\tposition: relative;\n\t\t}\n\n\t\t.filter-item {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\talign-items: center;\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #333;\n\t\t\theight: 100%;\n\t\t\t&.active-filter-item {\n\t\t\t\tcolor: #2E80FE;\n\t\t\t\tfont-weight: 600;\n\t\t\t}\n\t\t}\n\n\t\t.filter-item text {\n\t\t\tmargin-right: 10rpx;\n\t\t}\n\n\t\t.arrow {\n\t\t\tfont-size: 24rpx;\n\t\t\tcolor: #999;\n\t\t\ttransition: transform 0.3s ease;\n\t\t}\n\n\t\t.arrow.rotate {\n\t\t\ttransform: rotate(180deg);\n\t\t}\n\n\t\t.filter-dropdown {\n\t\t\tposition: absolute;\n\t\t\ttop: 100%;\n\t\t\tleft: 0;\n\t\t\twidth: 100%;\n\t\t\tmax-width: none;\n\t\t\ttransform: none;\n\t\t\tbackground-color: #fff;\n\t\t\tbox-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.15);\n\t\t\tz-index: 999;\n\t\t\tborder-radius: 0 0 12rpx 12rpx;\n\t\t}\n\n\t\t.dropdown-content {\n\t\t\tpadding: 30rpx;\n\t\t\tmax-height: 80vh;\n\t\t\toverflow-y: auto;\n\t\t}\n\n\t\t.filter-section {\n\t\t\tmargin-bottom: 30rpx;\n\t\t}\n\n\t\t.section-title {\n\t\t\tfont-size: 28rpx;\n\t\t\tcolor: #333;\n\t\t\tfont-weight: 500;\n\t\t\tmargin-bottom: 20rpx;\n\t\t}\n\n\t\t.option-list {\n\t\t\tdisplay: flex;\n\t\t\tflex-wrap: wrap;\n\t\t}\n\n\t\t.option-item {\n\t\t\tpadding: 15rpx 30rpx;\n\t\t\tbackground-color: #f5f5f5;\n\t\t\tborder-radius: 8rpx;\n\t\t\tmargin-right: 20rpx;\n\t\t\tmargin-bottom: 20rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tcolor: #666;\n\t\t}\n\n\t\t.option-item.active {\n\t\t\tbackground-color: #2E80FE;\n\t\t\tcolor: #fff;\n\t\t\tborder: 1px solid #2E80FE;\n\t\t}\n\n\t\t.custom-price-inputs {\n\t\t\tdisplay: flex;\n\t\t\talign-items: center;\n\t\t}\n\n\t\t.custom-price-inputs input {\n\t\t\tflex: 1;\n\t\t\theight: 70rpx;\n\t\t\tborder: 1rpx solid #eee;\n\t\t\tborder-radius: 8rpx;\n\t\t\tpadding: 0 20rpx;\n\t\t\tfont-size: 26rpx;\n\t\t\tbackground-color: #f5f5f5;\n\t\t}\n\n\t\t.custom-price-inputs text {\n\t\t\tmargin: 0 20rpx;\n\t\t\tcolor: #999;\n\t\t}\n\n\t\t.distance-input {\n\t\t\tpadding: 20rpx 0;\n\n\t\t\t> text {\n\t\t\t\tfont-size: 26rpx;\n\t\t\t\tcolor: #666;\n\t\t\t\tdisplay: block;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t}\n\n\t\t\t.distance-input-container {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tbackground-color: #f8f9fa;\n\t\t\t\tborder-radius: 8rpx;\n\t\t\t\tpadding: 0 20rpx;\n\t\t\t\tborder: 1rpx solid #e9ecef;\n\n\t\t\t\tinput {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 80rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tbackground: transparent;\n\t\t\t\t\tborder: none;\n\t\t\t\t\toutline: none;\n\t\t\t\t}\n\n\t\t\t\t.unit {\n\t\t\t\t\tfont-size: 26rpx;\n\t\t\t\t\tcolor: #666;\n\t\t\t\t\tmargin-left: 10rpx;\n\t\t\t\t\tflex-shrink: 0;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.distance-hint {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t\tmargin-top: 10rpx;\n\t\t\t\ttext-align: center;\n\t\t\t}\n\t\t}\n\n\t\t.filter-actions {\n\t\t\tdisplay: flex;\n\t\t\tpadding: 20rpx 0;\n\t\t\tborder-top: 1rpx solid #f0f0f0;\n\t\t\tmargin-top: 20rpx;\n\t\t}\n\n\t\t.filter-btn {\n\t\t\tflex: 1;\n\t\t\theight: 80rpx;\n\t\t\tline-height: 80rpx;\n\t\t\ttext-align: center;\n\t\t\tborder-radius: 8rpx;\n\t\t\tfont-size: 28rpx;\n\t\t}\n\n\t\t.filter-btn.reset {\n\t\t\tbackground-color: #fff;\n\t\t\tcolor: #666;\n\t\t\tborder: 1rpx solid #ccc;\n\t\t}\n\n\t\t.filter-btn.confirm {\n\t\t\tbackground-color: #2E80FE;\n\t\t\tcolor: #fff;\n\t\t\tmargin-left: 20rpx;\n\t\t}\n\t\t\n\t\t.quotation-counts {\n\t\t\tmargin: 10rpx 20rpx;\n\t\t\tpadding: 16rpx 20rpx;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 8rpx;\n\t\t\tbox-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.05);\n\n\t\t\t.counts-header {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: center;\n\t\t\t\tmargin-bottom: 12rpx;\n\n\t\t\t\t.counts-title {\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tfont-weight: 600;\n\t\t\t\t\tcolor: #333;\n\t\t\t\t\tposition: relative;\n\n\t\t\t\t\t&::after {\n\t\t\t\t\t\tcontent: '';\n\t\t\t\t\t\tposition: absolute;\n\t\t\t\t\t\tbottom: -4rpx;\n\t\t\t\t\t\tleft: 50%;\n\t\t\t\t\t\ttransform: translateX(-50%);\n\t\t\t\t\t\twidth: 30rpx;\n\t\t\t\t\t\theight: 2rpx;\n\t\t\t\t\t\tbackground: linear-gradient(90deg, #2E80FE, #4A9EFF);\n\t\t\t\t\t\tborder-radius: 1rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.counts-row {\n\t\t\t\tdisplay: flex;\n\t\t\t\talign-items: center;\n\t\t\t\tjustify-content: space-around;\n\n\t\t\t\t.count-item {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\talign-items: center;\n\n\t\t\t\t\t.count-label {\n\t\t\t\t\t\tfont-size: 22rpx;\n\t\t\t\t\t\tcolor: #666;\n\t\t\t\t\t\tmargin-bottom: 4rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t}\n\n\t\t\t\t\t.count-value {\n\t\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\t\tfont-weight: 700;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\tline-height: 1.2;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t.count-divider {\n\t\t\t\t\twidth: 1rpx;\n\t\t\t\t\theight: 40rpx;\n\t\t\t\t\tbackground-color: #e8e8e8;\n\t\t\t\t\tmargin: 0 20rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.check_box {\n\t\t\tmargin: 20rpx auto;\n\t\t\twidth: 690rpx;\n\t\t\tbackground-color: #fff;\n\t\t\tborder-radius: 10rpx;\n\t\t\toverflow: hidden;\n\t\t\t\n\t\t\t.collapse-container {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\t\n\t\t\t.collapse-item {\n\t\t\t\tborder-bottom: 1rpx solid #eee;\n\t\t\t}\n\t\t\t\n\t\t\t.collapse-header {\n\t\t\t\tdisplay: flex;\n\t\t\t\tjustify-content: space-between;\n\t\t\t\talign-items: center;\n\t\t\t\tpadding: 25rpx 30rpx;\n\t\t\t\tfont-size: 30rpx;\n\t\t\t\tcolor: #333;\n\t\t\t\tbackground-color: #fff;\n\t\t\t}\n\t\t\t\n\t\t\t.collapse-title {\n\t\t\t\tfont-weight: 500;\n\t\t\t}\n\t\t\t\n\t\t\t.collapse-icon {\n\t\t\t\tfont-size: 24rpx;\n\t\t\t\tcolor: #999;\n\t\t\t\ttransition: transform 0.3s ease;\n\t\t\t\t\n\t\t\t\t&.rotate {\n\t\t\t\t\ttransform: rotate(180deg);\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.collapse-content {\n\t\t\t\tpadding: 20rpx 30rpx;\n\t\t\t\tbackground-color: #f8f8f8;\n\t\t\t}\n\t\t\t\n\t\t\t.price-range, .distance-range, .cate-list {\n\t\t\t\twidth: 100%;\n\t\t\t}\n\t\t\t\n\t\t\t.price-options {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t\n\t\t\t\t.price-option {\n\t\t\t\t\twidth: calc(50% - 20rpx);\n\t\t\t\t\tmargin: 10rpx;\n\t\t\t\t\tpadding: 15rpx 0;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground-color: #2E80FE;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.custom-price {\n\t\t\t\tmargin-top: 20rpx;\n\t\t\t\t\n\t\t\t\t.custom-price-title {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tmargin-bottom: 15rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.custom-price-inputs {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\t\n\t\t\t\t\tinput {\n\t\t\t\t\t\tflex: 1;\n\t\t\t\t\t\theight: 70rpx;\n\t\t\t\t\t\tborder: 1rpx solid #eee;\n\t\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\t\tpadding: 0 20rpx;\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\ttext {\n\t\t\t\t\t\tmargin: 0 20rpx;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.distance-slider {\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\t\n\t\t\t\ttext {\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tdisplay: block;\n\t\t\t\t\tmargin-bottom: 20rpx;\n\t\t\t\t}\n\t\t\t\t\n\t\t\t\t.distance-labels {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tfont-size: 24rpx;\n\t\t\t\t\tcolor: #999;\n\t\t\t\t\tmargin-top: 10rpx;\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.cate-list {\n\t\t\t\tdisplay: flex;\n\t\t\t\tflex-wrap: wrap;\n\t\t\t\t\n\t\t\t\t.cate-item {\n\t\t\t\t\twidth: calc(33.33% - 20rpx);\n\t\t\t\t\tmargin: 10rpx;\n\t\t\t\t\tpadding: 15rpx 0;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\n\t\t\t\t\t&.active {\n\t\t\t\t\t\tbackground-color: #2E80FE;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\t\n\t\t\t.filter-actions {\n\t\t\t\tdisplay: flex;\n\t\t\t\tpadding: 20rpx 0;\n\t\t\t\t\n\t\t\t\t.filter-btn {\n\t\t\t\t\tflex: 1;\n\t\t\t\t\theight: 70rpx;\n\t\t\t\t\tline-height: 70rpx;\n\t\t\t\t\ttext-align: center;\n\t\t\t\t\tborder-radius: 8rpx;\n\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\tmargin: 0 10rpx;\n\t\t\t\t\t\n\t\t\t\t\t&.primary {\n\t\t\t\t\t\tbackground-color: #2E80FE;\n\t\t\t\t\t\tcolor: #fff;\n\t\t\t\t\t}\n\t\t\t\t\t\n\t\t\t\t\t&.plain {\n\t\t\t\t\t\tbackground-color: #fff;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t\tborder: 1rpx solid #2E80FE;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t.modal-content {\n\t\t\tpadding: 20rpx;\n\t\t\tmax-height: 400rpx;\n\t\t\toverflow-y: auto;\n\t\t}\n\n\t\t.re_item {\n\t\t\twidth: 690rpx;\n\t\t\tbackground-color: #fff;\n\t\t\tmargin: 20rpx auto;\n\t\t\tpadding: 40rpx;\n\t\t\tborder-radius: 10rpx; // Added border-radius for consistency\n\n\t\t\t.top {\n\t\t\t\tdisplay: flex;\n\n\t\t\t\timage {\n\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t}\n\n\t\t\t\t.order {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\tflex-direction: column;\n\t\t\t\t\tjustify-content: space-between;\n\t\t\t\t\tflex: 1; // Allows order content to take remaining space\n\n\t\t\t\t\t.title {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #171717;\n\t\t\t\t\t\tmax-width: 100%; // Ensure title doesn't overflow its parent\n\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t}\n\n\t\t\t\t\t.price {\n\t\t\t\t\t\tfont-size: 28rpx;\n\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\tcolor: #2E80FE;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.info {\n\t\t\t\tmargin-top: 40rpx;\n\n\t\t\t\t.address, .tel {\n\t\t\t\t\tdisplay: flex;\n\t\t\t\t\talign-items: center;\n\t\t\t\t\tmargin-bottom: 20rpx; // Spacing between address and tel\n\t\t\t\t\t&:last-child {\n\t\t\t\t\t\tmargin-bottom: 0; // No margin for the last one\n\t\t\t\t\t}\n\n\t\t\t\t\t.left {\n\t\t\t\t\t\tmargin-right: 20rpx;\n\t\t\t\t\t\tflex-shrink: 0; // Prevent icon from shrinking\n\t\t\t\t\t}\n\n\t\t\t\t\t.right {\n\t\t\t\t\t\tflex: 1; // Allows address/tel info to take remaining space\n\t\t\t\t\t\t.address_name {\n\t\t\t\t\t\t\tfont-size: 32rpx; // Adjusted font size for better readability\n\t\t\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\t\t\tcolor: #333333;\n\t\t\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t.address_Info {\n\t\t\t\t\t\t\tmargin-top: 8rpx; // Adjusted spacing\n\t\t\t\t\t\t\tfont-size: 26rpx; // Adjusted font size\n\t\t\t\t\t\t\tfont-weight: 400;\n\t\t\t\t\t\t\tcolor: #666666; // Slightly lighter color for info\n\t\t\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\t\t\toverflow: hidden;\n\t\t\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\t.tel .right {\n\t\t\t\t\tfont-size: 32rpx; // Consistent font size with address_name\n\t\t\t\t\tfont-weight: 500;\n\t\t\t\t\tcolor: #333333;\n\t\t\t\t\tmax-width: 100%;\n\t\t\t\t\toverflow: hidden;\n\t\t\t\t\twhite-space: nowrap;\n\t\t\t\t\ttext-overflow: ellipsis;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.notes {\n\t\t\t\tbackground-color: #f2f3f4;\n\t\t\t\tborder-radius: 5rpx;\n\t\t\t\tpadding: 15rpx; // Increased padding\n\t\t\t\tmargin-top: 20rpx; // Added margin top\n\t\t\t\tfont-size: 26rpx; // Added font size\n\t\t\t\tline-height: 1.5; // Added line height\n\n\t\t\t\tview {\n\t\t\t\t\tcolor:#999999;\n\t\t\t\t\tmargin-bottom: 5rpx;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t.btn {\n\t\t\t\tmargin: 40rpx auto 0; // Adjusted margin\n\t\t\t\twidth: 100%; // Full width within item padding\n\t\t\t\theight: 82rpx;\n\t\t\t\tborder-radius: 12rpx;\n\t\t\t\tborder: 2rpx solid #2E80FE;\n\t\t\t\tline-height: 82rpx;\n\t\t\t\ttext-align: center;\n\t\t\t\tfont-size: 32rpx;\n\t\t\t\tfont-weight: 500;\n\t\t\t\tcolor: #2E80FE;\n\t\t\t}\n\t\t}\n\n\t\t.loadmore {\n\t\t\tdisplay: flex;\n\t\t\tjustify-content: center;\n\t\t\tpadding: 20rpx 0; // Added padding\n\t\t}\n\n\t\t.footer {\n\t\t\tcolor: #333;\n\t\t\tmargin: 20rpx 0;\n\t\t\ttext-align: center;\n\t\t\tfont-size: 24rpx;\n\t\t}\n\t}\n</style>\n```", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./shifuIndex.vue?vue&type=style&index=0&id=5a79bfc8&scoped=true&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754721753290\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}