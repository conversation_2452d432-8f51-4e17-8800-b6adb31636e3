@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.test-update-page.data-v-59b4b70a {
  min-height: 100vh;
  background: #f5f5f5;
}
.navbar.data-v-59b4b70a {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
}
.navbar .nav-left.data-v-59b4b70a, .navbar .nav-right.data-v-59b4b70a {
  width: 80rpx;
}
.navbar .nav-icon.data-v-59b4b70a {
  font-size: 36rpx;
  color: #333;
  font-weight: bold;
}
.navbar .nav-title.data-v-59b4b70a {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.version-info.data-v-59b4b70a {
  padding: 40rpx 24rpx;
  background: #fff;
  margin-bottom: 20rpx;
  text-align: center;
}
.version-info .version-text.data-v-59b4b70a {
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}
.test-buttons.data-v-59b4b70a {
  padding: 0 24rpx;
  margin-bottom: 20rpx;
}
.test-buttons .test-btn.data-v-59b4b70a {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 44rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;
}
.test-buttons .test-btn.data-v-59b4b70a:disabled {
  background: #ccc;
}
.test-buttons .test-btn.data-v-59b4b70a:active:not(:disabled) {
  opacity: 0.8;
}
.log-section.data-v-59b4b70a {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
}
.log-section .log-title.data-v-59b4b70a {
  font-size: 30rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.log-section .log-content.data-v-59b4b70a {
  max-height: 600rpx;
  overflow-y: auto;
  background: #f8f8f8;
  border-radius: 8rpx;
  padding: 20rpx;
  margin-bottom: 20rpx;
}
.log-section .log-content .log-item.data-v-59b4b70a {
  display: block;
  font-size: 24rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 8rpx;
  word-break: break-all;
}
.log-section .clear-btn.data-v-59b4b70a {
  width: 100%;
  height: 60rpx;
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 30rpx;
  font-size: 28rpx;
}

