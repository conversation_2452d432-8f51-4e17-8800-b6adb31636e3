<template>
  <view class="test-update-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="nav-icon">‹</text>
      </view>
      <view class="nav-title">更新测试</view>
      <view class="nav-right"></view>
    </view>

    <!-- 当前版本信息 -->
    <view class="version-info">
      <text class="version-text">当前版本: v{{ currentVersion }}</text>
    </view>

    <!-- 测试按钮 -->
    <view class="test-buttons">
      <button 
        class="test-btn" 
        @click="testSilentCheck"
        :disabled="isChecking"
      >
        {{ isChecking ? '检查中...' : '测试静默检查' }}
      </button>
      
      <button 
        class="test-btn" 
        @click="testNormalCheck"
        :disabled="isChecking"
      >
        {{ isChecking ? '检查中...' : '测试普通检查' }}
      </button>
      
      <button 
        class="test-btn" 
        @click="testDirectAPI"
        :disabled="isChecking"
      >
        {{ isChecking ? '检查中...' : '测试直接API调用' }}
      </button>
    </view>

    <!-- 日志显示 -->
    <view class="log-section">
      <view class="log-title">测试日志</view>
      <view class="log-content">
        <text 
          class="log-item" 
          v-for="(log, index) in logs" 
          :key="index"
        >
          {{ log }}
        </text>
      </view>
      <button class="clear-btn" @click="clearLogs">清空日志</button>
    </view>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'
import $api from '@/api/index.js'

export default {
  name: 'TestUpdate',
  data() {
    return {
      currentVersion: '1.0.0',
      isChecking: false,
      logs: []
    }
  },
  onLoad() {
    this.getCurrentVersion()
    this.addLog('页面加载完成')
  },
  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 获取当前版本号
     */
    async getCurrentVersion() {
      this.currentVersion = await appUpdate.getCurrentVersion()
      this.addLog(`获取当前版本: ${this.currentVersion}`)
    },

    /**
     * 添加日志
     */
    addLog(message) {
      const time = new Date().toLocaleTimeString()
      this.logs.unshift(`[${time}] ${message}`)
      if (this.logs.length > 50) {
        this.logs = this.logs.slice(0, 50)
      }
    },

    /**
     * 清空日志
     */
    clearLogs() {
      this.logs = []
    },

    /**
     * 测试静默检查
     */
    async testSilentCheck() {
      this.isChecking = true
      this.addLog('开始测试静默检查...')
      
      try {
        const result = await appUpdate.checkUpdate({
          silent: true,
          showLoading: false
        })
        this.addLog(`静默检查完成，结果: ${result ? '有更新' : '无更新'}`)
      } catch (error) {
        this.addLog(`静默检查失败: ${error.message}`)
      } finally {
        this.isChecking = false
      }
    },

    /**
     * 测试普通检查
     */
    async testNormalCheck() {
      this.isChecking = true
      this.addLog('开始测试普通检查...')
      
      try {
        const result = await appUpdate.checkUpdate({
          silent: false,
          showLoading: true
        })
        this.addLog(`普通检查完成，结果: ${result ? '有更新' : '无更新'}`)
      } catch (error) {
        this.addLog(`普通检查失败: ${error.message}`)
      } finally {
        this.isChecking = false
      }
    },

    /**
     * 测试直接API调用
     */
    async testDirectAPI() {
      this.isChecking = true
      this.addLog('开始测试直接API调用...')
      
      try {
        const response = await $api.user.checkAppVersion({
          version: this.currentVersion,
          platform: 1  // 师傅端平台类型
        })
        
        this.addLog(`API调用成功: ${JSON.stringify(response)}`)
        
        if (response.code === '200' && response.data) {
          const updateInfo = response.data
          if (updateInfo.needUpdate) {
            this.addLog(`发现新版本: v${updateInfo.latestVersion}`)
            this.addLog(`更新描述: ${updateInfo.description}`)
            this.addLog(`是否强制更新: ${updateInfo.forceUpdate}`)
          } else {
            this.addLog('已是最新版本')
          }
        } else {
          this.addLog(`API返回错误: ${response.msg}`)
        }
      } catch (error) {
        this.addLog(`API调用失败: ${error.message}`)
      } finally {
        this.isChecking = false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.test-update-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  
  .nav-left, .nav-right {
    width: 80rpx;
  }
  
  .nav-icon {
    font-size: 36rpx;
    color: #333;
    font-weight: bold;
  }
  
  .nav-title {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }
}

.version-info {
  padding: 40rpx 24rpx;
  background: #fff;
  margin-bottom: 20rpx;
  text-align: center;
  
  .version-text {
    font-size: 32rpx;
    color: #333;
    font-weight: 500;
  }
}

.test-buttons {
  padding: 0 24rpx;
  margin-bottom: 20rpx;
  
  .test-btn {
    width: 100%;
    height: 88rpx;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
    border: none;
    border-radius: 44rpx;
    font-size: 32rpx;
    font-weight: 500;
    margin-bottom: 20rpx;
    
    &:disabled {
      background: #ccc;
    }
    
    &:active:not(:disabled) {
      opacity: 0.8;
    }
  }
}

.log-section {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 12rpx;
  padding: 32rpx 24rpx;
  
  .log-title {
    font-size: 30rpx;
    color: #333;
    font-weight: 600;
    margin-bottom: 20rpx;
  }
  
  .log-content {
    max-height: 600rpx;
    overflow-y: auto;
    background: #f8f8f8;
    border-radius: 8rpx;
    padding: 20rpx;
    margin-bottom: 20rpx;
    
    .log-item {
      display: block;
      font-size: 24rpx;
      color: #666;
      line-height: 1.6;
      margin-bottom: 8rpx;
      word-break: break-all;
    }
  }
  
  .clear-btn {
    width: 100%;
    height: 60rpx;
    background: #f0f0f0;
    color: #666;
    border: none;
    border-radius: 30rpx;
    font-size: 28rpx;
  }
}
</style>
