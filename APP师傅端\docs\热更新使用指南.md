# 今师傅APP热更新使用指南

## 快速开始

### 方法一：直接调用更新工具

```javascript
import appUpdate from '@/utils/app-update.js'

// 检查更新（显示提示）
appUpdate.checkUpdate({
    silent: false,
    showLoading: true
})

// 静默检查（不显示提示）
appUpdate.checkUpdate({
    silent: true,
    showLoading: false
})

// 获取当前版本
const version = appUpdate.getCurrentVersion()
```

### 方法二：使用更新组件

```vue
<template>
  <view>
    <!-- 在任意页面中使用更新检查组件 -->
    <app-update-check></app-update-check>
  </view>
</template>

<script>
import AppUpdateCheck from '@/components/app-update-check.vue'

export default {
  components: {
    AppUpdateCheck
  }
}
</script>
```

### 方法三：跳转到更新页面

```javascript
// 跳转到专门的更新页面
uni.navigateTo({
    url: '/pages/app-update'
})
```

## 后端接口配置

### 接口地址
```
POST /api/user/login/checkAppVersion
```

### 请求参数
```json
{
    "version": "*******",  // 当前版本号
    "platform": 1         // 平台类型：1-师傅端，2-用户端
}
```

### 响应格式
```json
{
    "code": "200",
    "msg": "success",
    "data": {
        "needUpdate": true,
        "latestVersion": "1.0.3",
        "wgtUrl": "https://example.com/app_v1.0.3.wgt",
        "description": "修复部分已知问题，优化用户体验",
        "forceUpdate": 0,
        "platform": 1
    }
}
```

## API 方法说明

### checkUpdate(options)
检查版本更新

**参数**:
- `options.silent` (Boolean): 是否静默检查，默认 false
- `options.showLoading` (Boolean): 是否显示加载提示，默认 true

**返回值**: Promise<Object|null>
- 有更新时返回更新信息对象
- 无更新时返回 null

**示例**:
```javascript
try {
    const updateInfo = await appUpdate.checkUpdate({
        silent: false,
        showLoading: true
    })
    
    if (updateInfo) {
        console.log('发现新版本:', updateInfo.latestVersion)
    } else {
        console.log('已是最新版本')
    }
} catch (error) {
    console.error('检查更新失败:', error)
}
```

### getCurrentVersion()
获取当前APP版本号

**返回值**: Promise<String>

**示例**:
```javascript
const version = await appUpdate.getCurrentVersion()
console.log('当前版本:', version)
```

### downloadAndInstall(updateInfo)
下载并安装更新包

**参数**:
- `updateInfo` (Object): 更新信息对象

**示例**:
```javascript
appUpdate.downloadAndInstall({
    latestVersion: '1.0.3',
    wgtUrl: 'https://example.com/app_v1.0.3.wgt',
    description: '更新说明',
    forceUpdate: false
})
```

### silentUpdate(updateInfo)
静默更新（后台下载，下次启动生效）

**参数**:
- `updateInfo` (Object): 更新信息对象

### clearVersionCache()
清除版本缓存

**示例**:
```javascript
appUpdate.clearVersionCache()
```

## 组件使用

### app-update-check 组件

这是一个即插即用的更新检查组件，可以直接在页面中使用。

**特性**:
- 自动获取并显示当前版本号
- 点击触发更新检查
- 美观的UI界面

**使用方法**:
```vue
<template>
  <view>
    <app-update-check></app-update-check>
  </view>
</template>

<script>
import AppUpdateCheck from '@/components/app-update-check.vue'

export default {
  components: {
    AppUpdateCheck
  }
}
</script>
```

## 页面使用

### app-update 页面

专门的更新页面，提供完整的更新功能界面。

**功能**:
- 显示当前版本信息
- 手动检查更新
- 显示更新信息和进度
- 跳转到测试页面

**跳转方法**:
```javascript
uni.navigateTo({
    url: '/pages/app-update'
})
```

### test-update 页面

更新功能测试页面，用于开发调试。

**功能**:
- 测试静默检查
- 测试普通检查
- 测试直接API调用
- 显示详细日志

**跳转方法**:
```javascript
uni.navigateTo({
    url: '/pages/test-update'
})
```

## 自定义配置

### 修改平台类型

在 `utils/app-update.js` 中修改：
```javascript
constructor() {
    this.platform = 1 // 1-师傅端，2-用户端
}
```

### 修改接口地址

在 `api/modules/user.js` 中修改：
```javascript
checkAppVersion(param) {
    return req.post("your/custom/api/path", param)
}
```

### 自定义更新对话框

```javascript
// 重写 showUpdateDialog 方法
appUpdate.showUpdateDialog = function(updateInfo) {
    // 自定义对话框逻辑
    uni.showModal({
        title: '发现新版本',
        content: updateInfo.description,
        success: (res) => {
            if (res.confirm) {
                this.downloadAndInstall(updateInfo)
            }
        }
    })
}
```

## 最佳实践

### 1. APP启动时自动检查

在 `App.vue` 的 `onLaunch` 中添加：
```javascript
onLaunch() {
    // 延迟3秒后检查更新
    setTimeout(() => {
        appUpdate.checkUpdate({
            silent: true,
            showLoading: false
        })
    }, 3000)
}
```

### 2. 设置页面添加更新入口

```vue
<template>
  <view class="setting-item" @click="checkUpdate">
    <text>检查更新</text>
    <text>v{{ currentVersion }}</text>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'

export default {
  data() {
    return {
      currentVersion: '1.0.0'
    }
  },
  async mounted() {
    this.currentVersion = await appUpdate.getCurrentVersion()
  },
  methods: {
    checkUpdate() {
      uni.navigateTo({
        url: '/pages/app-update'
      })
    }
  }
}
</script>
```

### 3. 错误处理

```javascript
try {
    await appUpdate.checkUpdate()
} catch (error) {
    console.error('更新检查失败:', error)
    
    // 可以选择性地显示错误提示
    if (!silent) {
        uni.showToast({
            title: '检查更新失败，请稍后重试',
            icon: 'none'
        })
    }
}
```

## 注意事项

1. **权限要求**: 确保APP有安装应用的权限
2. **网络环境**: 更新功能需要网络连接
3. **存储空间**: 确保设备有足够空间下载更新包
4. **版本格式**: 建议使用语义化版本号格式
5. **测试环境**: 在发布前充分测试更新功能
