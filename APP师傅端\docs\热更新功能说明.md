# 今师傅APP热更新功能说明

## 功能概述

本项目已集成APP热更新功能，支持自动检查版本更新、下载安装包、静默更新等特性。热更新功能基于自定义后端接口实现，支持强制更新和可选更新两种模式。

## 功能特点

- ✅ 自动版本检查（APP启动时）
- ✅ 手动检查更新（设置页面）
- ✅ 强制更新支持
- ✅ 静默更新支持
- ✅ 下载进度显示
- ✅ 安装完成后重启提示
- ✅ 友好的用户界面

## 技术实现

### 1. 后端接口

**接口地址**: `/api/user/login/checkAppVersion`

**请求参数**:
```json
{
    "version": "*******",  // 当前版本号
    "platform": 1         // 平台类型：1-师傅端，2-用户端
}
```

**返回数据**:
```json
{
    "code": "200",
    "msg": "",
    "data": {
        "needUpdate": true,           // 是否需要更新
        "latestVersion": "1.0.3",     // 最新版本号
        "wgtUrl": "https://example.com/app_v1.0.3.wgt", // 下载地址
        "description": "修复部分已知问题2", // 更新说明
        "forceUpdate": 1,             // 是否强制更新：1-强制，0-可选
        "platform": 1                // 平台类型
    }
}
```

### 2. 核心文件

- `utils/app-update.js` - 更新工具类
- `components/app-update-check.vue` - 更新检查组件
- `pages/app-update.vue` - 更新页面
- `pages/test-update.vue` - 测试页面

### 3. 版本检查流程

```mermaid
graph TD
    A[APP启动] --> B[获取当前版本]
    B --> C[调用检查更新接口]
    C --> D{是否有新版本?}
    D -->|是| E[显示更新对话框]
    D -->|否| F[继续使用]
    E --> G{是否强制更新?}
    G -->|是| H[强制下载安装]
    G -->|否| I{用户选择}
    I -->|立即更新| H
    I -->|稍后更新| F
    H --> J[下载更新包]
    J --> K[安装更新包]
    K --> L[重启应用]
```

## 使用说明

### 1. 自动检查更新

APP启动后会自动在后台检查更新（延迟3秒），如果发现新版本会弹出更新提示。

### 2. 手动检查更新

用户可以在"我的"页面点击"检查更新"进入更新页面，手动检查并下载更新。

### 3. 强制更新

当后端返回`forceUpdate: 1`时，用户必须更新才能继续使用APP。

### 4. 静默更新

支持后台静默下载更新包，下次启动时自动生效（需要后端配置支持）。

## 配置说明

### 1. 版本号配置

在`manifest.json`中配置当前版本：
```json
{
    "versionName": "1.1.2",  // 版本名称
    "versionCode": 112       // 版本代码
}
```

### 2. 平台类型配置

在`utils/app-update.js`中配置平台类型：
```javascript
this.platform = 1 // 1-师傅端，2-用户端
```

### 3. API接口配置

在`api/modules/user.js`中已添加版本检查接口：
```javascript
checkAppVersion(param) {
    return req.post("user/login/checkAppVersion", param)
}
```

## 开发调试

### 1. 测试页面

访问 `/pages/test-update` 可以进行各种更新功能测试：
- 静默检查测试
- 普通检查测试
- 直接API调用测试

### 2. 日志输出

所有更新相关操作都会输出详细日志，便于调试：
```javascript
console.log('检查更新 - 当前版本:', currentVersion)
console.log('检查更新 - 服务器响应:', response)
```

### 3. 版本缓存清理

如需清理版本缓存，可调用：
```javascript
appUpdate.clearVersionCache()
```

## 注意事项

1. **网络环境**: 确保设备网络连接正常
2. **存储空间**: 确保设备有足够存储空间下载更新包
3. **权限配置**: 确保APP有安装应用的权限
4. **版本格式**: 版本号格式应为 `x.x.x.x` 格式
5. **平台区分**: 师傅端和用户端使用不同的平台标识

## 常见问题

### Q: 检查更新失败怎么办？
A: 检查网络连接和后端接口是否正常，查看控制台日志获取详细错误信息。

### Q: 下载失败怎么办？
A: 检查下载链接是否有效，网络是否稳定，存储空间是否充足。

### Q: 安装失败怎么办？
A: 检查APP权限设置，确保有安装应用的权限，或尝试手动安装。

### Q: 如何测试更新功能？
A: 使用测试页面进行各种场景测试，或修改当前版本号进行模拟测试。
