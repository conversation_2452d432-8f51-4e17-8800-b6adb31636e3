{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/app-update.vue?e102", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/app-update.vue?1599", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/app-update.vue?e560", "uni-app:///pages/app-update.vue", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/app-update.vue?f7cc", "webpack:///C:/Users/<USER>/Desktop/APP (2)/8-5APP/APP师傅端/pages/app-update.vue?5448"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "name", "data", "currentVersion", "isChecking", "isUpdating", "updateInfo", "showNoUpdate", "onLoad", "methods", "goBack", "uni", "getCurrentVersion", "appUpdate", "checkUpdate", "originalShowDialog", "silent", "showLoading", "setTimeout", "console", "title", "icon", "startUpdate", "laterUpdate", "goToTestPage", "url", "goToDebugPage"], "mappings": ";;;;;;;;;;;;;AAAA;AAE2D;AAC3D;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,kBAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAmI;AACnI;AAC8D;AACL;AACsC;;;AAG/F;AACsN;AACtN,gBAAgB,8NAAU;AAC1B,EAAE,gFAAM;AACR,EAAE,iGAAM;AACR,EAAE,0GAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,qGAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACRA;AAAA;AAAA;AAAA;AAAy1B,CAAgB,y2BAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;AC+F72B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAC;EACAC;IACA;MACAC;MACAC;MACAC;MACAC;MACAC;IACA;EACA;EACAC;IACA;EACA;EACAC;IACA;AACA;AACA;IACAC;MACAC;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;UAAA;YAAA;cAAA;gBAAA;gBAAA,OACAC;cAAA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IAEA;AACA;AACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACA;gBACA;gBAAA;gBAGA;gBACAC;gBACAF;kBACA;gBACA;gBAAA;gBAAA,OAEAA;kBAAAG;kBAAAC;gBAAA;cAAA;gBAEA;gBACAJ;;gBAEA;gBACA;kBACA;kBACAK;oBACA;kBACA;gBACA;gBAAA;gBAAA;cAAA;gBAAA;gBAAA;gBAEAC;gBACAR;kBACAS;kBACAC;gBACA;cAAA;gBAAA;gBAEA;gBAAA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IAEA;IAEA;AACA;AACA;IACAC;MAAA;MACA;MAEA;;MAEA;MACAT;;MAEA;MACAK;QACA;MACA;IACA;IAEA;AACA;AACA;IACAK;MACA;MACAZ;QACAS;QACAC;MACA;IACA;IAEA;AACA;AACA;IACAG;MACAb;QACAc;MACA;IACA;IAEA;AACA;AACA;IACAC;MACAf;QACAc;MACA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AClNA;AAAA;AAAA;AAAA;AAAgmD,CAAgB,ojDAAG,EAAC,C;;;;;;;;;;;ACApnD;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/app-update.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;import '@dcloudio/uni-stat/dist/uni-stat.es.js';\nimport Vue from 'vue'\nimport Page from './pages/app-update.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./app-update.vue?vue&type=template&id=05362ea4&scoped=true&\"\nvar renderjs\nimport script from \"./app-update.vue?vue&type=script&lang=js&\"\nexport * from \"./app-update.vue?vue&type=script&lang=js&\"\nimport style0 from \"./app-update.vue?vue&type=style&index=0&id=05362ea4&lang=scss&scoped=true&\"\n\n\n/* normalize component */\nimport normalizer from \"!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\runtime\\\\componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  \"05362ea4\",\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/app-update.vue\"\nexport default component.exports", "export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\templateLoader.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--17-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\template.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-uni-app-loader\\\\page-meta.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app-update.vue?vue&type=template&id=05362ea4&scoped=true&\"", "var components\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app-update.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\babel-loader\\\\lib\\\\index.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--13-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\script.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app-update.vue?vue&type=script&lang=js&\"", "<template>\n  <view class=\"app-update-page\">\n    <!-- 导航栏 -->\n    <view class=\"navbar\">\n      <view class=\"nav-left\" @click=\"goBack\">\n        <text class=\"iconfont icon-arrow-left\"></text>\n      </view>\n      <view class=\"nav-title\">版本更新</view>\n      <view class=\"nav-right\"></view>\n    </view>\n\n    <!-- 当前版本信息 -->\n    <view class=\"version-info\">\n      <view class=\"version-icon\">\n        <text class=\"iconfont icon-mobile\"></text>\n      </view>\n      <view class=\"version-details\">\n        <text class=\"version-name\">今师傅接单版</text>\n        <text class=\"version-number\">当前版本: v{{ currentVersion }}</text>\n      </view>\n    </view>\n\n    <!-- 检查更新按钮 -->\n    <view class=\"check-update-section\">\n      <button\n        class=\"check-btn\"\n        @click=\"checkUpdate\"\n        :disabled=\"isChecking\"\n      >\n        {{ isChecking ? '检查中...' : '检查更新' }}\n      </button>\n\n      <button\n        class=\"test-btn\"\n        @click=\"goToTestPage\"\n      >\n        测试页面\n      </button>\n\n      <button\n        class=\"debug-btn\"\n        @click=\"goToDebugPage\"\n      >\n        调试页面\n      </button>\n    </view>\n\n    <!-- 更新信息 -->\n    <view class=\"update-info\" v-if=\"updateInfo\">\n      <view class=\"info-header\">\n        <text class=\"info-title\">发现新版本 v{{ updateInfo.latestVersion }}</text>\n        <view class=\"update-badge\" v-if=\"updateInfo.forceUpdate\">强制更新</view>\n      </view>\n      \n      <view class=\"info-content\">\n        <text class=\"info-desc\">{{ updateInfo.description }}</text>\n      </view>\n      \n      <view class=\"info-actions\">\n        <button \n          class=\"update-btn primary\" \n          @click=\"startUpdate\"\n          :disabled=\"isUpdating\"\n        >\n          {{ isUpdating ? '更新中...' : '立即更新' }}\n        </button>\n        <button \n          class=\"update-btn secondary\" \n          @click=\"laterUpdate\"\n          v-if=\"!updateInfo.forceUpdate && !isUpdating\"\n        >\n          稍后更新\n        </button>\n      </view>\n    </view>\n\n    <!-- 无更新提示 -->\n    <view class=\"no-update\" v-if=\"showNoUpdate\">\n      <text class=\"no-update-text\">🎉 已是最新版本</text>\n    </view>\n\n    <!-- 更新说明 -->\n    <view class=\"update-notes\">\n      <view class=\"notes-title\">更新说明</view>\n      <view class=\"notes-content\">\n        <text class=\"notes-item\">• 支持热更新功能，及时获取最新版本</text>\n        <text class=\"notes-item\">• 优化用户体验，提升应用性能</text>\n        <text class=\"notes-item\">• 修复已知问题，增强应用稳定性</text>\n        <text class=\"notes-item\">• 建议及时更新以获得最佳使用体验</text>\n      </view>\n    </view>\n  </view>\n</template>\n\n<script>\nimport appUpdate from '@/utils/app-update.js'\n\nexport default {\n  name: 'AppUpdate',\n  data() {\n    return {\n      currentVersion: '1.0.0',\n      isChecking: false,\n      isUpdating: false,\n      updateInfo: null,\n      showNoUpdate: false\n    }\n  },\n  onLoad() {\n    this.getCurrentVersion()\n  },\n  methods: {\n    /**\n     * 返回上一页\n     */\n    goBack() {\n      uni.navigateBack()\n    },\n\n    /**\n     * 获取当前版本号\n     */\n    async getCurrentVersion() {\n      this.currentVersion = await appUpdate.getCurrentVersion()\n    },\n\n    /**\n     * 检查更新\n     */\n    async checkUpdate() {\n      this.isChecking = true\n      this.updateInfo = null\n      this.showNoUpdate = false\n\n      try {\n        // 重写appUpdate的showUpdateDialog方法，不显示弹窗\n        const originalShowDialog = appUpdate.showUpdateDialog\n        appUpdate.showUpdateDialog = (updateInfo) => {\n          this.updateInfo = updateInfo\n        }\n\n        await appUpdate.checkUpdate({ silent: true, showLoading: false })\n\n        // 恢复原方法\n        appUpdate.showUpdateDialog = originalShowDialog\n\n        // 如果没有更新信息，说明已是最新版本\n        if (!this.updateInfo) {\n          this.showNoUpdate = true\n          setTimeout(() => {\n            this.showNoUpdate = false\n          }, 3000)\n        }\n      } catch (error) {\n        console.error('检查更新失败:', error)\n        uni.showToast({\n          title: '检查更新失败',\n          icon: 'none'\n        })\n      } finally {\n        this.isChecking = false\n      }\n    },\n\n    /**\n     * 开始更新\n     */\n    startUpdate() {\n      if (!this.updateInfo) return\n\n      this.isUpdating = true\n      \n      // 使用原生的下载安装方法\n      appUpdate.downloadAndInstall(this.updateInfo)\n      \n      // 监听安装完成（这里简化处理）\n      setTimeout(() => {\n        this.isUpdating = false\n      }, 1000)\n    },\n\n    /**\n     * 稍后更新\n     */\n    laterUpdate() {\n      this.updateInfo = null\n      uni.showToast({\n        title: '您可以稍后在设置中检查更新',\n        icon: 'none'\n      })\n    },\n\n    /**\n     * 跳转到测试页面\n     */\n    goToTestPage() {\n      uni.navigateTo({\n        url: '/pages/test-update'\n      })\n    },\n\n    /**\n     * 跳转到调试页面\n     */\n    goToDebugPage() {\n      uni.navigateTo({\n        url: '/pages/debug-update'\n      })\n    }\n  }\n}\n</script>\n\n<style lang=\"scss\" scoped>\n.app-update-page {\n  min-height: 100vh;\n  background: #f5f5f5;\n}\n\n.navbar {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  height: 88rpx;\n  padding: 0 24rpx;\n  background: #fff;\n  border-bottom: 1rpx solid #eee;\n  position: sticky;\n  top: 0;\n  z-index: 100;\n}\n\n.nav-left, .nav-right {\n  width: 80rpx;\n  height: 60rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n}\n\n.nav-left .iconfont {\n  font-size: 36rpx;\n  color: #333;\n}\n\n.nav-title {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.version-info {\n  display: flex;\n  align-items: center;\n  padding: 40rpx 24rpx;\n  background: #fff;\n  margin: 20rpx;\n  border-radius: 16rpx;\n}\n\n.version-icon {\n  width: 120rpx;\n  height: 120rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  border-radius: 24rpx;\n  display: flex;\n  align-items: center;\n  justify-content: center;\n  margin-right: 24rpx;\n}\n\n.version-icon .iconfont {\n  font-size: 60rpx;\n  color: #fff;\n}\n\n.version-details {\n  flex: 1;\n}\n\n.version-name {\n  font-size: 36rpx;\n  font-weight: 600;\n  color: #333;\n  display: block;\n  margin-bottom: 8rpx;\n}\n\n.version-number {\n  font-size: 28rpx;\n  color: #666;\n  display: block;\n}\n\n.check-update-section {\n  padding: 0 24rpx;\n  margin-bottom: 20rpx;\n}\n\n.check-btn {\n  width: 100%;\n  height: 88rpx;\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: #fff;\n  border: none;\n  border-radius: 16rpx;\n  font-size: 32rpx;\n  font-weight: 600;\n  margin-bottom: 20rpx;\n\n  &:disabled {\n    opacity: 0.6;\n  }\n}\n\n.test-btn {\n  width: 100%;\n  height: 88rpx;\n  background: #f0f0f0;\n  color: #666;\n  border: none;\n  border-radius: 16rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n  margin-bottom: 20rpx;\n\n  &:active {\n    opacity: 0.8;\n  }\n}\n\n.debug-btn {\n  width: 100%;\n  height: 88rpx;\n  background: #ff9800;\n  color: #fff;\n  border: none;\n  border-radius: 16rpx;\n  font-size: 32rpx;\n  font-weight: 500;\n\n  &:active {\n    opacity: 0.8;\n  }\n}\n\n.update-info {\n  background: #fff;\n  margin: 0 24rpx 20rpx;\n  border-radius: 16rpx;\n  padding: 32rpx;\n}\n\n.info-header {\n  display: flex;\n  align-items: center;\n  justify-content: space-between;\n  margin-bottom: 20rpx;\n}\n\n.info-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n}\n\n.update-badge {\n  background: #ff4757;\n  color: #fff;\n  font-size: 22rpx;\n  padding: 8rpx 16rpx;\n  border-radius: 20rpx;\n}\n\n.info-content {\n  margin-bottom: 32rpx;\n}\n\n.info-desc {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.6;\n}\n\n.info-actions {\n  display: flex;\n  gap: 20rpx;\n}\n\n.update-btn {\n  flex: 1;\n  height: 80rpx;\n  border: none;\n  border-radius: 12rpx;\n  font-size: 30rpx;\n  font-weight: 600;\n\n  &.primary {\n    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n    color: #fff;\n  }\n\n  &.secondary {\n    background: #f8f9fa;\n    color: #666;\n    border: 1rpx solid #e9ecef;\n  }\n\n  &:disabled {\n    opacity: 0.6;\n  }\n}\n\n.no-update {\n  background: #fff;\n  margin: 0 24rpx 20rpx;\n  border-radius: 16rpx;\n  padding: 60rpx 32rpx;\n  text-align: center;\n}\n\n.no-update-text {\n  font-size: 32rpx;\n  color: #52c41a;\n  font-weight: 600;\n}\n\n.update-notes {\n  background: #fff;\n  margin: 0 24rpx;\n  border-radius: 16rpx;\n  padding: 32rpx;\n}\n\n.notes-title {\n  font-size: 32rpx;\n  font-weight: 600;\n  color: #333;\n  margin-bottom: 20rpx;\n}\n\n.notes-content {\n  display: flex;\n  flex-direction: column;\n  gap: 16rpx;\n}\n\n.notes-item {\n  font-size: 28rpx;\n  color: #666;\n  line-height: 1.5;\n}\n</style>\n", "import mod from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app-update.vue?vue&type=style&index=0&id=05362ea4&lang=scss&scoped=true&\"; export default mod; export * from \"-!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\mini-css-extract-plugin\\\\dist\\\\loader.js??ref--8-oneOf-1-0!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\css-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-1!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\loaders\\\\stylePostLoader.js!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-2!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\postcss-loader\\\\src\\\\index.js??ref--8-oneOf-1-3!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\sass-loader\\\\dist\\\\cjs.js??ref--8-oneOf-1-4!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\webpack-preprocess-loader\\\\index.js??ref--8-oneOf-1-5!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\vue-cli-plugin-uni\\\\packages\\\\vue-loader\\\\lib\\\\index.js??vue-loader-options!E:\\\\BaiduNetdiskDownload\\\\HBuilderX.4.45.2025010502\\\\HBuilderX\\\\plugins\\\\uniapp-cli\\\\node_modules\\\\@dcloudio\\\\webpack-uni-mp-loader\\\\lib\\\\style.js!./app-update.vue?vue&type=style&index=0&id=05362ea4&lang=scss&scoped=true&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1754721705755\n      var cssReload = require(\"E:/BaiduNetdiskDownload/HBuilderX.4.45.2025010502/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}