@charset "UTF-8";
/**
 * 这里是uni-app内置的常用样式变量
 *
 * uni-app 官方扩展插件及插件市场（https://ext.dcloud.net.cn）上很多三方插件均使用了这些样式变量
 * 如果你是插件开发者，建议你使用scss预处理，并在插件代码中直接使用这些变量（无需 import 这个文件），方便用户通过搭积木的方式开发整体风格一致的App
 *
 */
/**
 * 如果你是App开发者（插件使用者），你可以通过修改这些变量来定制自己的插件主题，实现自定义主题功能
 *
 * 如果你的项目同样使用了scss预处理，你也可以直接在你的 scss 代码中使用如下变量，同时无需 import 这个文件
 */
.app-update-page.data-v-05362ea4 {
  min-height: 100vh;
  background: #f5f5f5;
}
.navbar.data-v-05362ea4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  position: -webkit-sticky;
  position: sticky;
  top: 0;
  z-index: 100;
}
.nav-left.data-v-05362ea4, .nav-right.data-v-05362ea4 {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}
.nav-left .iconfont.data-v-05362ea4 {
  font-size: 36rpx;
  color: #333;
}
.nav-title.data-v-05362ea4 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}
.version-info.data-v-05362ea4 {
  display: flex;
  align-items: center;
  padding: 40rpx 24rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}
.version-icon.data-v-05362ea4 {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}
.version-icon .iconfont.data-v-05362ea4 {
  font-size: 60rpx;
  color: #fff;
}
.version-details.data-v-05362ea4 {
  flex: 1;
}
.version-name.data-v-05362ea4 {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}
.version-number.data-v-05362ea4 {
  font-size: 28rpx;
  color: #666;
  display: block;
}
.check-update-section.data-v-05362ea4 {
  padding: 0 24rpx;
  margin-bottom: 20rpx;
}
.check-btn.data-v-05362ea4 {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;
}
.check-btn.data-v-05362ea4:disabled {
  opacity: 0.6;
}
.test-btn.data-v-05362ea4 {
  width: 100%;
  height: 88rpx;
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
}
.test-btn.data-v-05362ea4:active {
  opacity: 0.8;
}
.update-info.data-v-05362ea4 {
  background: #fff;
  margin: 0 24rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}
.info-header.data-v-05362ea4 {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}
.info-title.data-v-05362ea4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}
.update-badge.data-v-05362ea4 {
  background: #ff4757;
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}
.info-content.data-v-05362ea4 {
  margin-bottom: 32rpx;
}
.info-desc.data-v-05362ea4 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}
.info-actions.data-v-05362ea4 {
  display: flex;
  gap: 20rpx;
}
.update-btn.data-v-05362ea4 {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;
}
.update-btn.primary.data-v-05362ea4 {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}
.update-btn.secondary.data-v-05362ea4 {
  background: #f8f9fa;
  color: #666;
  border: 1rpx solid #e9ecef;
}
.update-btn.data-v-05362ea4:disabled {
  opacity: 0.6;
}
.no-update.data-v-05362ea4 {
  background: #fff;
  margin: 0 24rpx 20rpx;
  border-radius: 16rpx;
  padding: 60rpx 32rpx;
  text-align: center;
}
.no-update-text.data-v-05362ea4 {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
}
.update-notes.data-v-05362ea4 {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}
.notes-title.data-v-05362ea4 {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}
.notes-content.data-v-05362ea4 {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}
.notes-item.data-v-05362ea4 {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

