<template>
  <view class="app-update-page">
    <!-- 导航栏 -->
    <view class="navbar">
      <view class="nav-left" @click="goBack">
        <text class="iconfont icon-arrow-left"></text>
      </view>
      <view class="nav-title">版本更新</view>
      <view class="nav-right"></view>
    </view>

    <!-- 当前版本信息 -->
    <view class="version-info">
      <view class="version-icon">
        <text class="iconfont icon-mobile"></text>
      </view>
      <view class="version-details">
        <text class="version-name">今师傅接单版</text>
        <text class="version-number">当前版本: v{{ currentVersion }}</text>
      </view>
    </view>

    <!-- 检查更新按钮 -->
    <view class="check-update-section">
      <button
        class="check-btn"
        @click="checkUpdate"
        :disabled="isChecking"
      >
        {{ isChecking ? '检查中...' : '检查更新' }}
      </button>

      <button
        class="test-btn"
        @click="goToTestPage"
      >
        测试页面
      </button>

      <button
        class="debug-btn"
        @click="goToDebugPage"
      >
        调试页面
      </button>
    </view>

    <!-- 更新信息 -->
    <view class="update-info" v-if="updateInfo">
      <view class="info-header">
        <text class="info-title">发现新版本 v{{ updateInfo.latestVersion }}</text>
        <view class="update-badge" v-if="updateInfo.forceUpdate">强制更新</view>
      </view>
      
      <view class="info-content">
        <text class="info-desc">{{ updateInfo.description }}</text>
      </view>
      
      <view class="info-actions">
        <button 
          class="update-btn primary" 
          @click="startUpdate"
          :disabled="isUpdating"
        >
          {{ isUpdating ? '更新中...' : '立即更新' }}
        </button>
        <button 
          class="update-btn secondary" 
          @click="laterUpdate"
          v-if="!updateInfo.forceUpdate && !isUpdating"
        >
          稍后更新
        </button>
      </view>
    </view>

    <!-- 无更新提示 -->
    <view class="no-update" v-if="showNoUpdate">
      <text class="no-update-text">🎉 已是最新版本</text>
    </view>

    <!-- 更新说明 -->
    <view class="update-notes">
      <view class="notes-title">更新说明</view>
      <view class="notes-content">
        <text class="notes-item">• 支持热更新功能，及时获取最新版本</text>
        <text class="notes-item">• 优化用户体验，提升应用性能</text>
        <text class="notes-item">• 修复已知问题，增强应用稳定性</text>
        <text class="notes-item">• 建议及时更新以获得最佳使用体验</text>
      </view>
    </view>
  </view>
</template>

<script>
import appUpdate from '@/utils/app-update.js'

export default {
  name: 'AppUpdate',
  data() {
    return {
      currentVersion: '1.0.0',
      isChecking: false,
      isUpdating: false,
      updateInfo: null,
      showNoUpdate: false
    }
  },
  onLoad() {
    this.getCurrentVersion()
  },
  methods: {
    /**
     * 返回上一页
     */
    goBack() {
      uni.navigateBack()
    },

    /**
     * 获取当前版本号
     */
    async getCurrentVersion() {
      this.currentVersion = await appUpdate.getCurrentVersion()
    },

    /**
     * 检查更新
     */
    async checkUpdate() {
      this.isChecking = true
      this.updateInfo = null
      this.showNoUpdate = false

      try {
        // 重写appUpdate的showUpdateDialog方法，不显示弹窗
        const originalShowDialog = appUpdate.showUpdateDialog
        appUpdate.showUpdateDialog = (updateInfo) => {
          this.updateInfo = updateInfo
        }

        await appUpdate.checkUpdate({ silent: true, showLoading: false })

        // 恢复原方法
        appUpdate.showUpdateDialog = originalShowDialog

        // 如果没有更新信息，说明已是最新版本
        if (!this.updateInfo) {
          this.showNoUpdate = true
          setTimeout(() => {
            this.showNoUpdate = false
          }, 3000)
        }
      } catch (error) {
        console.error('检查更新失败:', error)
        uni.showToast({
          title: '检查更新失败',
          icon: 'none'
        })
      } finally {
        this.isChecking = false
      }
    },

    /**
     * 开始更新
     */
    startUpdate() {
      if (!this.updateInfo) return

      this.isUpdating = true
      
      // 使用原生的下载安装方法
      appUpdate.downloadAndInstall(this.updateInfo)
      
      // 监听安装完成（这里简化处理）
      setTimeout(() => {
        this.isUpdating = false
      }, 1000)
    },

    /**
     * 稍后更新
     */
    laterUpdate() {
      this.updateInfo = null
      uni.showToast({
        title: '您可以稍后在设置中检查更新',
        icon: 'none'
      })
    },

    /**
     * 跳转到测试页面
     */
    goToTestPage() {
      uni.navigateTo({
        url: '/pages/test-update'
      })
    },

    /**
     * 跳转到调试页面
     */
    goToDebugPage() {
      uni.navigateTo({
        url: '/pages/debug-update'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.app-update-page {
  min-height: 100vh;
  background: #f5f5f5;
}

.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #eee;
  position: sticky;
  top: 0;
  z-index: 100;
}

.nav-left, .nav-right {
  width: 80rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-left .iconfont {
  font-size: 36rpx;
  color: #333;
}

.nav-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.version-info {
  display: flex;
  align-items: center;
  padding: 40rpx 24rpx;
  background: #fff;
  margin: 20rpx;
  border-radius: 16rpx;
}

.version-icon {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
}

.version-icon .iconfont {
  font-size: 60rpx;
  color: #fff;
}

.version-details {
  flex: 1;
}

.version-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  display: block;
  margin-bottom: 8rpx;
}

.version-number {
  font-size: 28rpx;
  color: #666;
  display: block;
}

.check-update-section {
  padding: 0 24rpx;
  margin-bottom: 20rpx;
}

.check-btn {
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 20rpx;

  &:disabled {
    opacity: 0.6;
  }
}

.test-btn {
  width: 100%;
  height: 88rpx;
  background: #f0f0f0;
  color: #666;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;
  margin-bottom: 20rpx;

  &:active {
    opacity: 0.8;
  }
}

.debug-btn {
  width: 100%;
  height: 88rpx;
  background: #ff9800;
  color: #fff;
  border: none;
  border-radius: 16rpx;
  font-size: 32rpx;
  font-weight: 500;

  &:active {
    opacity: 0.8;
  }
}

.update-info {
  background: #fff;
  margin: 0 24rpx 20rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.info-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 20rpx;
}

.info-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.update-badge {
  background: #ff4757;
  color: #fff;
  font-size: 22rpx;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.info-content {
  margin-bottom: 32rpx;
}

.info-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.info-actions {
  display: flex;
  gap: 20rpx;
}

.update-btn {
  flex: 1;
  height: 80rpx;
  border: none;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 600;

  &.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: #fff;
  }

  &.secondary {
    background: #f8f9fa;
    color: #666;
    border: 1rpx solid #e9ecef;
  }

  &:disabled {
    opacity: 0.6;
  }
}

.no-update {
  background: #fff;
  margin: 0 24rpx 20rpx;
  border-radius: 16rpx;
  padding: 60rpx 32rpx;
  text-align: center;
}

.no-update-text {
  font-size: 32rpx;
  color: #52c41a;
  font-weight: 600;
}

.update-notes {
  background: #fff;
  margin: 0 24rpx;
  border-radius: 16rpx;
  padding: 32rpx;
}

.notes-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
}

.notes-content {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
}

.notes-item {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
</style>
